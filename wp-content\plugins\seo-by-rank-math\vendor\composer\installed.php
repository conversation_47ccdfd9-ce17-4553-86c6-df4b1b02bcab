<?php return array(
    'root' => array(
        'name' => 'rankmath/seo-by-rank-math',
        'pretty_version' => 'v1.0.245',
        'version' => '1.0.245.0',
        'reference' => 'c22017147b46ee13b9c8d3eaafe54527e7feae48',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'a5hleyrich/wp-background-processing' => array(
            'pretty_version' => '1.4.0',
            'version' => '1.4.0.0',
            'reference' => '7ca7cc3504333db3a291bbab7f1917124fba4816',
            'type' => 'library',
            'install_path' => __DIR__ . '/../a5hleyrich/wp-background-processing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cmb2/cmb2' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'reference' => '2847828b5cce1b48d09427ee13e6f7c752704468',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../cmb2/cmb2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'donatj/phpuseragentparser' => array(
            'pretty_version' => 'v1.10.0',
            'version' => '1.10.0.0',
            'reference' => '3ba73057d2a4a275badb88b7708e91e159c40367',
            'type' => 'library',
            'install_path' => __DIR__ . '/../donatj/phpuseragentparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mythemeshop/wordpress-helpers' => array(
            'pretty_version' => 'v1.1.22',
            'version' => '1.1.22.0',
            'reference' => '0ee4a87a03dac72e9e503cc4e1b0208d3269f4dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mythemeshop/wordpress-helpers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rankmath/seo-by-rank-math' => array(
            'pretty_version' => 'v1.0.245',
            'version' => '1.0.245.0',
            'reference' => 'c22017147b46ee13b9c8d3eaafe54527e7feae48',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/action-scheduler' => array(
            'pretty_version' => '3.9.2',
            'version' => '3.9.2.0',
            'reference' => 'efbb7953f72a433086335b249292f280dd43ddfe',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../woocommerce/action-scheduler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
