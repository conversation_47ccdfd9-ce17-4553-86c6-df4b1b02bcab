<mddoc>
	<docpage target="README.md">

		<autoloader namespace="donatj\UserAgent" type="psr4" root="src" />

		<section title="PHP User Agent Parser">
			<text><![CDATA[
[![Join the chat at https://gitter.im/PhpUserAgentParser/Lobby](https://badges.gitter.im/PhpUserAgentParser/Lobby.svg)](https://gitter.im/PhpUserAgentParser/Lobby?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)
]]></text>
			<badge-poser type="version"/>
			<badge-poser type="downloads"/>
			<badge-poser type="license"/>
			<badge-github-action name="donatj/phpUserAgent" workflow-file="ci.yml"/>
			<section title="What It Is">
				<text><![CDATA[
A simple, streamlined PHP user-agent parser!

Licensed under the MIT license: https://www.opensource.org/licenses/mit-license.php
]]></text>
			</section>
			<section title="Upgrading to `1.*`">
				<text><![CDATA[
The new `1.*` release **does not break compatibility** with `0.*` and nothing need to change to upgrade. However, the global `parse_user_agent` is now deprecated; it has been replaced with the namespaced `\donatj\UserAgent\parse_user_agent` and functions exactly the same. You can easily replace any existing call to `parse_user_agent` with `\donatj\UserAgent\parse_user_agent`

In addition, 1.x adds a convenience object wrapper you may use should you prefer. More information on this is in the Usage section below.
]]></text>
			</section>
			<section title="Why Use This">
				<text><![CDATA[
You have your choice in user-agent parsers. This one detects **all modern browsers** in a very light, quick, understandable fashion.
It is less than 200 lines of code, and consists of just three regular expressions!
It can also correctly identify exotic versions of IE others fail on.

It offers 100% unit test coverage, is installable via Composer, and is very easy to use.
]]></text>
			</section>
			<section title="What It Does Not Do">
				<text><![CDATA[
This is not meant as a browser "knowledge engine" but rather a simple parser. Anything not adequately provided directly by the user agent string itself will simply not be provided by this.
]]></text>

				<section title="OS Versions">
					<text><![CDATA[
User-agent strings **are not** a reliable source of OS Version!

- Many agents simply don't send the information.
- Others provide varying levels of accuracy.
- Parsing Windows versions alone almost nearly doubles the size of the code.

I'm much more interested in keeping this thing *tiny* and accurate than adding niché features and would rather focus on things that can be **done well**.

All that said, there is the start of a [branch to do it](https://github.com/donatj/PhpUserAgent/tree/os_version_detection) I created for a client if you want to poke it, I update it from time to time, but frankly if you need to *reliably detect OS Version*, using user-agent isn't the way to do it. I'd go with JavaScript.
]]></text>
				</section>
				<section title="Undetectable Browsers">
					<text><![CDATA[
- **Brave** - Brave is simply not differentiable from Chrome. This was a design decision on their part.
]]></text>
				</section>
				<section title="Undetectable Platforms">
					<text><![CDATA[
- **iPadOS 13+** - Starting with iPadOS 13 and further hardened in 14, iPadOS returns the **exact** same string as macOS. It is no longer distinguishable by UA string. (See: [#69](https://github.com/donatj/PhpUserAgent/issues/69))
]]></text>
				</section>
			</section>
			<section title="Requirements">
				<composer-requires/>
			</section>
			<section title="Installing">
				<text>PHP User Agent is available through Packagist via Composer.</text>
				<composer-install/>
			</section>
			<section title="Usage">
				<text>The classic procedural use is as simple as:</text>
				<source name="examples/procedural.php" lang="php" />

				<text>The new object-oriented wrapper form:</text>
				<source name="examples/object-oriented.php" lang="php" />
			</section>
			<section title="Currently Detected Platforms">
				<exec cmd="php .helpers/constants.php 'donatj\UserAgent\Platforms' 'Platform'"/>
			</section>
			<section title="Currently Detected Browsers">
				<exec cmd="php .helpers/constants.php 'donatj\UserAgent\Browsers' 'Browser'"/>
			</section>
			<text><![CDATA[
More information is available at [Donat Studios](https://donatstudios.com/PHP-Parser-HTTP_USER_AGENT).
]]></text>
		</section>
	</docpage>
</mddoc>
