{"packages": [{"name": "a5hleyrich/wp-background-processing", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/deliciousbrains/wp-background-processing.git", "reference": "7ca7cc3504333db3a291bbab7f1917124fba4816"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deliciousbrains/wp-background-processing/zipball/7ca7cc3504333db3a291bbab7f1917124fba4816", "reference": "7ca7cc3504333db3a291bbab7f1917124fba4816", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpcompatibility/phpcompatibility-wp": "*", "phpunit/phpunit": "^8.0", "spryker/code-sniffer": "^0.17.18", "wp-coding-standards/wpcs": "^2.3", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"coenjacobs/mozart": "Easily wrap this library with your own prefix, to prevent collisions when multiple plugins use this library"}, "time": "2024-12-17T14:04:30+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["classes/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Delicious Brains", "email": "<EMAIL>"}], "description": "WP Background Processing can be used to fire off non-blocking asynchronous requests or as a background processing tool, allowing you to queue tasks.", "support": {"issues": "https://github.com/deliciousbrains/wp-background-processing/issues", "source": "https://github.com/deliciousbrains/wp-background-processing/tree/1.4.0"}, "install-path": "../a5hleyrich/wp-background-processing"}, {"name": "cmb2/cmb2", "version": "v2.11.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/CMB2/CMB2.git", "reference": "2847828b5cce1b48d09427ee13e6f7c752704468"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CMB2/CMB2/zipball/2847828b5cce1b48d09427ee13e6f7c752704468", "reference": "2847828b5cce1b48d09427ee13e6f7c752704468", "shasum": ""}, "require": {"php": ">7.4"}, "require-dev": {"apigen/apigen": "4.1.2", "awesomemotive/am-cli-tools": ">=1.3.7", "nette/utils": "2.5.3", "phpunit/phpunit": "^6.5", "yoast/phpunit-polyfills": "^1.1"}, "suggest": {"composer/installers": "~1.0"}, "time": "2024-04-02T19:30:07+00:00", "type": "wordpress-plugin", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://dsgnwrks.pro", "role": "Developer"}, {"name": "WebDevStudios", "email": "<EMAIL>", "homepage": "https://github.com/WebDevStudios", "role": "Developer"}], "description": "CMB2 is a metabox, custom fields, and forms library for WordPress that will blow your mind.", "homepage": "https://github.com/CMB2/CMB2", "keywords": ["metabox", "plugin", "wordpress"], "support": {"issues": "https://github.com/CMB2/CMB2/issues", "source": "http://wordpress.org/support/plugin/cmb2"}, "install-path": "../cmb2/cmb2"}, {"name": "donatj/phpuseragentparser", "version": "v1.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/donatj/PhpUserAgent.git", "reference": "3ba73057d2a4a275badb88b7708e91e159c40367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/donatj/PhpUserAgent/zipball/3ba73057d2a4a275badb88b7708e91e159c40367", "reference": "3ba73057d2a4a275badb88b7708e91e159c40367", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=5.4.0"}, "require-dev": {"camspiers/json-pretty": "~1.0", "donatj/drop": "*", "ext-json": "*", "phpunit/phpunit": "~4.8|~9"}, "time": "2024-10-30T15:45:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/UserAgentParser.php"], "psr-4": {"donatj\\UserAgent\\": "src/UserAgent"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://donatstudios.com", "role": "Developer"}], "description": "Lightning fast, minimalist PHP UserAgent string parser.", "homepage": "https://donatstudios.com/PHP-Parser-HTTP_USER_AGENT", "keywords": ["browser", "browser detection", "parser", "user agent", "useragent"], "support": {"issues": "https://github.com/donatj/PhpUserAgent/issues", "source": "https://github.com/donatj/PhpUserAgent/tree/v1.10.0"}, "funding": [{"url": "https://www.paypal.me/donatj/15", "type": "custom"}, {"url": "https://github.com/donatj", "type": "github"}, {"url": "https://ko-fi.com/donatj", "type": "ko_fi"}], "install-path": "../donatj/phpuseragentparser"}, {"name": "mythemeshop/wordpress-helpers", "version": "v1.1.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/MyThemeShopTeam/wordpress-helpers.git", "reference": "0ee4a87a03dac72e9e503cc4e1b0208d3269f4dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyThemeShopTeam/wordpress-helpers/zipball/0ee4a87a03dac72e9e503cc4e1b0208d3269f4dc", "reference": "0ee4a87a03dac72e9e503cc4e1b0208d3269f4dc", "shasum": ""}, "require": {"php": ">=5.6"}, "time": "2023-06-15T08:54:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MyThemeShop\\Helpers\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "MyThemeShop", "email": "<EMAIL>"}], "description": "Collection of utilities required during development of a plugin or theme for WordPress. Built for developers by developers.", "support": {"issues": "https://github.com/MyThemeShopTeam/wordpress-helpers/issues", "source": "https://github.com/MyThemeShopTeam/wordpress-helpers/tree/v1.1.22"}, "install-path": "../mythemeshop/wordpress-helpers"}, {"name": "woocommerce/action-scheduler", "version": "3.9.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/woocommerce/action-scheduler.git", "reference": "efbb7953f72a433086335b249292f280dd43ddfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/action-scheduler/zipball/efbb7953f72a433086335b249292f280dd43ddfe", "reference": "efbb7953f72a433086335b249292f280dd43ddfe", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5", "woocommerce/woocommerce-sniffs": "0.1.0", "wp-cli/wp-cli": "~2.5.0", "yoast/phpunit-polyfills": "^2.0"}, "time": "2025-02-03T09:09:30+00:00", "type": "wordpress-plugin", "extra": {"scripts-description": {"test": "Run unit tests", "phpcs": "Analyze code against the WordPress coding standards with PHP_CodeSniffer", "phpcbf": "Fix coding standards warnings/errors automatically with PHP Code Beautifier"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "Action Scheduler for WordPress and WooCommerce", "homepage": "https://actionscheduler.org/", "support": {"issues": "https://github.com/woocommerce/action-scheduler/issues", "source": "https://github.com/woocommerce/action-scheduler/tree/3.9.2"}, "install-path": "../woocommerce/action-scheduler"}], "dev": false, "dev-package-names": []}