# Copyright (C) 2025 Rank Math SEO
# This file is distributed under the GPL-3.0+.
msgid ""
msgstr ""
"Project-Id-Version: Rank Math SEO 1.0.245\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/seo-by-rank-math\n"
"Last-Translator: Rank Math <<EMAIL>>\n"
"Language-Team: Rank Math <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-05-21T11:26:19+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.12.0\n"
"X-Domain: rank-math\n"

#. Plugin Name of the plugin
#. Author of the plugin
msgid "Rank Math SEO"
msgstr ""

#. Plugin URI of the plugin
msgid "https://rankmath.com/"
msgstr ""

#. Description of the plugin
msgid "Rank Math SEO is the Best WordPress SEO plugin with the features of many SEO and AI SEO tools in a single package to help multiply your SEO traffic."
msgstr ""

#. Author URI of the plugin
msgid "https://rankmath.com/?utm_source=Plugin&utm_medium=Readme%20Author%20URI&utm_campaign=WP"
msgstr ""

msgid "Please use the correct format."
msgstr ""

msgid "This field is required."
msgstr ""

msgid "Please enter a valid email address."
msgstr ""

msgid "Please enter a valid URL."
msgstr ""

msgid "Rank Math Dashboard"
msgstr ""

msgid "Dashboard"
msgstr ""

msgid "Homepage SEO"
msgstr ""

msgid "Edit Homepage SEO Settings"
msgstr ""

msgid "Pages"
msgstr ""

#. translators: Post Type Singular Name
#. translators: Taxonomy Singular Name
msgid "SEO Settings for %s"
msgstr ""

msgid "Edit default SEO settings for this post type"
msgstr ""

msgid "Edit SEO settings for this archive page"
msgstr ""

msgid "SEO Settings for Date Archives"
msgstr ""

msgid "SEO Settings for Search Page"
msgstr ""

msgid "Edit SEO settings for the search results page"
msgstr ""

msgid "Mark this page"
msgstr ""

msgid "As Pillar Content"
msgstr ""

msgid "As NoIndex"
msgstr ""

msgid "As NoFollow"
msgstr ""

msgid "External Tools"
msgstr ""

msgid "Google PageSpeed"
msgstr ""

msgid "Google PageSpeed Insights"
msgstr ""

msgid "Google Rich Results (Mobile)"
msgstr ""

msgid "Google Rich Results Test - Googlebot Smartphone"
msgstr ""

msgid "Google Rich Results (Desktop)"
msgstr ""

msgid "Google Rich Results Test - Googlebot Desktop"
msgstr ""

msgid "Facebook Debugger"
msgstr ""

msgid "Facebook Sharing Debugger"
msgstr ""

msgid "Modules"
msgstr ""

msgid "Help"
msgstr ""

msgid "Setup Wizard"
msgstr ""

msgid "Import &amp; Export"
msgstr ""

msgid "Rank Math Knowledge Base"
msgstr ""

msgid "Search Options"
msgstr ""

msgid "Easy Mode"
msgstr ""

msgid "Advanced Mode"
msgstr ""

msgid "None"
msgstr ""

msgid "Unable to validate Rank Math SEO registration data."
msgstr ""

msgid "Please try reconnecting."
msgstr ""

#. translators: KB Link
msgid "If the issue persists, please try the solution described in our Knowledge Base article: %s"
msgstr ""

msgid "[3. Unable to Encrypt]"
msgstr ""

msgid "Seems like your site URL has changed since you connected to Rank Math."
msgstr ""

msgid "Click here to reconnect."
msgstr ""

#. translators: sitename
msgid "I just installed @RankMathSEO #WordPress Plugin. It looks great! %s"
msgstr ""

#. translators: sitename
msgid "I just installed Rank Math SEO WordPress Plugin. It looks promising!"
msgstr ""

msgid "SEO by Rank Math"
msgstr ""

msgid "Tweet"
msgstr ""

msgid "Share"
msgstr ""

#. Translators: 1 is "WordPress Address (URL)", 2 is "Site Address (URL)", 3 is a link to the General Settings, with "WordPress General Settings" as anchor text.
msgid "Rank Math cannot be connected because your site URL doesn't appear to be a valid URL. If the domain name contains special characters, please make sure to use the encoded version in the %1$s &amp; %2$s fields on the %3$s page."
msgstr ""

msgid "WordPress Address (URL)"
msgstr ""

msgid "Site Address (URL)"
msgstr ""

msgid "WordPress General Settings"
msgstr ""

msgid "Rank Math"
msgstr ""

msgid "Help &amp; Support"
msgstr ""

msgid "Christmas Sale"
msgstr ""

msgid "New Year Sale"
msgstr ""

msgid "Anniversary Sale"
msgstr ""

msgid "Black Friday Sale"
msgstr ""

msgid "Cyber Monday Sale"
msgstr ""

msgid "Twitter username (without @)"
msgstr ""

msgid "Facebook profile URL"
msgstr ""

msgid "Additional profile URLs"
msgstr ""

msgid "The canonical URL you entered does not seem to be a valid URL. Please double check it in the SEO meta box &raquo; Advanced tab."
msgstr ""

msgid "Copy Link URL to Clipboard"
msgstr ""

msgid "Insert Link in Content"
msgstr ""

msgid "You are not authorized to perform this action."
msgstr ""

msgid "Exclusive Offer!"
msgstr ""

msgid "Additional Profiles to add in the <code>sameAs</code> Schema property."
msgstr ""

#. Translators: placeholder is the plugin name.
msgid "Hey, we noticed you've been using %s for more than a week now – that's awesome!"
msgstr ""

#. Translators: placeholder is the plugin name.
msgctxt "plugin name inside the review notice"
msgid "Rank Math SEO"
msgstr ""

msgid "Could you please do us a BIG favor and give it a rating on WordPress.org to help us spread the word and boost our motivation?"
msgstr ""

msgid "Co-founder of Rank Math"
msgstr ""

msgid "Yes, you deserve it"
msgstr ""

msgid "No, maybe later"
msgstr ""

msgid "I already did"
msgstr ""

#. translators: plugin url
msgid "Thank you for using <a href=\"%s\" target=\"_blank\">Rank Math</a>"
msgstr ""

msgid "Insert/edit link"
msgstr ""

msgid "Update"
msgstr ""

msgid "Add Link"
msgstr ""

msgid "(no title)"
msgstr ""

msgid "No matches found."
msgstr ""

msgid "Link selected."
msgstr ""

msgid "Link inserted."
msgstr ""

msgid "Add <code>rel=\"nofollow\"</code>"
msgstr ""

msgid "Add <code>rel=\"sponsored\"</code>"
msgstr ""

msgid "Link Title"
msgstr ""

msgid "&#8595; Rank Math"
msgstr ""

msgid "Set to noindex"
msgstr ""

msgid "Set to index"
msgstr ""

msgid "Set to nofollow"
msgstr ""

msgid "Set to follow"
msgstr ""

msgid "Remove custom canonical URL"
msgstr ""

msgid "Redirect"
msgstr ""

msgid "Remove redirection"
msgstr ""

msgid "Set Schema: None"
msgstr ""

#. Translators: placeholder is the default Schema type setting.
msgid "Set Schema: Default (%s)"
msgstr ""

msgid "Determine Search Intent"
msgstr ""

msgid "Your current plugin version does not support this feature. Please update Rank Math PRO to version 3.0.83 or later to unlock full functionality."
msgstr ""

msgid "Off"
msgstr ""

msgid "On"
msgstr ""

msgid "Snippet"
msgstr ""

msgid "Specify a maximum text-length, in characters, of a snippet for your page."
msgstr ""

msgid "Video Preview"
msgstr ""

msgid "Specify a maximum duration in seconds of an animated video preview."
msgstr ""

msgid "Image Preview"
msgstr ""

msgid "Specify a maximum size of image preview to be shown for images on this page."
msgstr ""

msgid "Large"
msgstr ""

msgid "Standard"
msgstr ""

msgid "Rank Math Overview"
msgstr ""

msgid "Latest Blog Posts from Rank Math"
msgstr ""

msgid "Error: the Rank Math blog feed could not be downloaded."
msgstr ""

msgid "NEW"
msgstr ""

msgid "Blog"
msgstr ""

msgid "(opens in a new window)"
msgstr ""

msgid "Go Pro"
msgstr ""

#. translators: Plugin name
msgid "Cleanup of %s data successfully done."
msgstr ""

#. translators: Plugin name
msgid "Cleanup of %s data failed."
msgstr ""

msgid "Action not allowed."
msgstr ""

msgid "No items found."
msgstr ""

#. Translators: placeholder is the post type name.
msgid "Rank Math has detected a new post type: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a>."
msgstr ""

#. Translators: placeholder is the post type names separated with commas.
msgid "Rank Math has detected new post types: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a>."
msgstr ""

msgid "Please activate the WPML String Translation plugin to convert Rank Math Setting values in different languages."
msgstr ""

msgid "<b>Rank Math Warning:</b> Changing the permalinks on a live, indexed site may result in serious loss of traffic if done incorrectly. Consider adding a new redirection from the old URL format to the new one."
msgstr ""

msgid "Links"
msgstr ""

#. translators: Link to kb article
msgid "Change how some of the links open and operate on your website. %s."
msgstr ""

#. translators: Link to kb article
#. translators: Link to social setting KB article
msgid "Learn More"
msgstr ""

msgid "Breadcrumbs"
msgstr ""

#. translators: Link to kb article
msgid "Here you can set up the breadcrumbs function. %s"
msgstr ""

#. translators: Link to kb article
#. translators: Link to KB article
#. translators: Redirection page url
#. translators: 1. Link to KB article 2. Link to redirection setting scree
#. translators: Learn more link.
#. translators: %1$s: thing, %2$s: Learn more link.
msgid "Learn more"
msgstr ""

msgid "Use the following code in your theme template files to display breadcrumbs."
msgstr ""

msgid "Webmaster Tools"
msgstr ""

#. translators: Link to kb article
msgid "Enter verification codes for third-party webmaster tools. %s"
msgstr ""

msgid "Others"
msgstr ""

#. translators: Link to kb article
msgid "Change some uncommon but essential settings here. %s."
msgstr ""

msgid "Edit .htaccess"
msgstr ""

#. translators: Link to kb article
msgid "Edit the contents of your .htaccess file easily. %s."
msgstr ""

msgid "SEO Settings"
msgstr ""

msgid "General Settings"
msgstr ""

msgid "Global Meta"
msgstr ""

#. translators: Link to KB article
msgid "Change Global meta settings that take effect across your website. %s."
msgstr ""

msgid "Local SEO"
msgstr ""

#. translators: Redirection page url
msgid "Optimize for local searches and Knowledge Graph using these settings. %s."
msgstr ""

msgid "Use the <code>[rank_math_contact_info]</code> shortcode to display contact information in a nicely formatted way. You should also claim your business on Google if you have not already."
msgstr ""

msgid "Social Meta"
msgstr ""

#. translators: Link to social setting KB article
msgid "Add social account information to your website's Schema and Open Graph. %s."
msgstr ""

msgid "Homepage"
msgstr ""

#. translators: Link to KB article
msgid "Add SEO meta and OpenGraph details to your homepage. %s."
msgstr ""

msgid "Authors"
msgstr ""

#. translators: Link to KB article
msgid "Change SEO options related to the author archives. %s."
msgstr ""

msgid "Misc Pages"
msgstr ""

#. translators: Link to KB article
msgid "Customize SEO meta settings of pages like search results, 404s, etc. %s."
msgstr ""

msgid "SEO Titles &amp; Meta"
msgstr ""

msgid "Titles &amp; Meta"
msgstr ""

msgid "Post Types:"
msgstr ""

msgid "Attachments"
msgstr ""

#. translators: 1. post type name 2. link
#. translators: 1. taxonomy name 2. link
msgid "Change Global SEO, Schema, and other settings for %1$s. %2$s"
msgstr ""

msgid "Post Formats"
msgstr ""

msgid "Post Formats Archive"
msgstr ""

msgid "You do not have permission to edit the .htaccess file."
msgstr ""

msgid "Failed to backup .htaccess file. Please check file permissions."
msgstr ""

msgid "Failed to update .htaccess file. Please check file permissions."
msgstr ""

msgid ".htaccess file updated successfully."
msgstr ""

msgid "Panel"
msgstr ""

msgid "Take your SEO to the Next Level!"
msgstr ""

msgid "Get Rank Math PRO!"
msgstr ""

msgid "Click here to see all the exciting features."
msgstr ""

msgid "Ctrl/Cmd + Enter"
msgstr ""

msgid "Save Changes"
msgstr ""

msgid "Reset Options"
msgstr ""

msgid "$id variable required"
msgstr ""

msgid "Variable Required"
msgstr ""

msgid "$title variable required"
msgstr ""

msgid "SEO Details"
msgstr ""

msgid "SEO Title"
msgstr ""

msgid "SEO Desc"
msgstr ""

msgid "Title"
msgstr ""

msgid "Alternative Text"
msgstr ""

msgid "Save"
msgstr ""

msgid "Cancel"
msgstr ""

msgid "No Index"
msgstr ""

msgid "Is Pillar"
msgstr ""

msgid "Focus Keyword"
msgstr ""

msgid "Keyword"
msgstr ""

msgid "Not Set"
msgstr ""

msgid "SEO Score: Good"
msgstr ""

msgid "SEO Score: Ok"
msgstr ""

msgid "SEO Score: Bad"
msgstr ""

msgid "Focus Keyword Not Set"
msgstr ""

msgid "Articles noindexed"
msgstr ""

msgid "Pillar Content"
msgstr ""

msgid "Rank Your Content With the Power of PRO & A.I."
msgstr ""

msgid "Unlimited Websites"
msgstr ""

msgid "Content A.I. (Artificial Intelligence)"
msgstr ""

msgid "Keyword Rank Tracker"
msgstr ""

msgid "Powerful Schema Generator"
msgstr ""

msgid "24x7 Premium Support"
msgstr ""

msgid "SEO Email Reports"
msgstr ""

msgid "and Many More…"
msgstr ""

msgid "Yes, I want to learn more"
msgstr ""

msgid "No, I don't want it"
msgstr ""

msgid "I already upgraded"
msgstr ""

msgid "Yes, I want better SEO"
msgstr ""

msgid "I already purchased"
msgstr ""

msgid "Rank Math plugin could not be connected."
msgstr ""

msgid "Unable to connect Rank Math."
msgstr ""

msgid "Import Schemas"
msgstr ""

msgid "Import all Schema data for Posts, Pages, and custom post types."
msgstr ""

msgid "Import Settings"
msgstr ""

msgid "Import AIO SEO plugin settings, global meta, sitemap settings, etc."
msgstr ""

msgid "Import Post Meta"
msgstr ""

msgid "Import meta information of your posts/pages like the titles, descriptions, robots meta, OpenGraph info, etc."
msgstr ""

msgid "Import Author Meta"
msgstr ""

msgid "Import Social URLs of your author archive pages."
msgstr ""

msgid "Import Term Meta"
msgstr ""

msgid "Import meta information of your terms like the titles, descriptions, robots meta, OpenGraph info, etc."
msgstr ""

msgid "Import Redirections"
msgstr ""

msgid "Import all the redirections you have already set up in AIO SEO Premium."
msgstr ""

msgid "Import Locations"
msgstr ""

msgid "Import Locations Settings."
msgstr ""

msgid "HTML Sitemap"
msgstr ""

msgid "Import plugin settings, global meta, sitemap settings, etc."
msgstr ""

msgid "Import meta information of your posts/pages like the focus keyword, titles, descriptions, robots meta, OpenGraph info, etc."
msgstr ""

msgid "Import data like category, tag, and CPT meta data from SEO."
msgstr ""

msgid "Import meta information like titles, descriptions, focus keyword, robots meta, etc., of your author archive pages."
msgstr ""

msgid "Import all the redirections you have already set up in Yoast Premium."
msgstr ""

msgid "Import Blocks"
msgstr ""

msgid "Import and convert all compatible blocks in post contents."
msgstr ""

msgid "Import Locations Settings from Yoast plugin."
msgstr ""

msgid "Import News Settings"
msgstr ""

msgid "Import News Settings from Yoast News Add-on."
msgstr ""

msgid "Import Video Sitemap Settings"
msgstr ""

msgid "Import Video Sitemap Settings from Yoast Video Add-on."
msgstr ""

msgid "Unable to perform action this time."
msgstr ""

msgid "Settings imported successfully."
msgstr ""

msgid "News Settings imported successfully."
msgstr ""

msgid "Video Settings imported successfully."
msgstr ""

msgid "Plugin deactivated successfully."
msgstr ""

#. translators: start, end, total
msgid "Imported post meta for posts %1$s - %2$s out of %3$s "
msgstr ""

#. translators: total
msgid "Imported term meta for %s terms."
msgstr ""

#. translators: start, end, total
msgid "Imported user meta for users %1$s - %2$s out of %3$s "
msgstr ""

#. translators: total
msgid "Imported %s redirections."
msgstr ""

#. translators: start, end, total
msgid "Imported blocks from posts %1$s - %2$s out of %3$s "
msgstr ""

#. translators: start, end, total
msgid "Recalculating scores for posts %1$s - %2$s... "
msgstr ""

msgid "Settings import failed."
msgstr ""

msgid "Posts meta import failed."
msgstr ""

msgid "Term meta import failed."
msgstr ""

msgid "User meta import failed."
msgstr ""

msgid "There are no redirection to import."
msgstr ""

msgid "Blocks import failed."
msgstr ""

msgid "Plugin settings and site-wide meta data."
msgstr ""

msgid "No post found."
msgstr ""

msgid "Link Suggestions"
msgstr ""

msgid "Click on the button to copy URL or insert link in content. You can also drag and drop links in the post content."
msgstr ""

msgid "We can't show any link suggestions for this post. Try selecting categories and tags for this post, and mark other posts as Pillar Content to make them show up here."
msgstr ""

msgid "The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr ""

msgid "Upgrade"
msgstr ""

msgid "username"
msgstr ""

msgid "Description"
msgstr ""

msgid "Next steps&hellip;"
msgstr ""

msgid "Upgrade to PRO"
msgstr ""

msgid "Advanced Schema, Analytics and much more..."
msgstr ""

msgid "Setup Rank Math"
msgstr ""

msgid "How to Properly Setup Rank Math"
msgstr ""

msgid "Import Data"
msgstr ""

msgid "How to Import Data from Your Previous SEO Plugin"
msgstr ""

msgid "Improve SEO Score"
msgstr ""

msgid "How to Make Your Posts Pass All the Tests"
msgstr ""

msgid "Product Support"
msgstr ""

msgid "Online Documentation"
msgstr ""

msgid "Understand all the capabilities of Rank Math"
msgstr ""

msgid "Ticket Support"
msgstr ""

msgid "Direct help from our qualified support team"
msgstr ""

msgid "Affiliate Program"
msgstr ""

msgid "Earn flat 30% on every sale!"
msgstr ""

msgid "Account"
msgstr ""

msgid "Connected"
msgstr ""

msgid "Not Connected"
msgstr ""

#. translators: variables used to wrap the text in the strong tag.
msgid "The plugin is currently not connected with your Rank Math account. Click on the button below to login or register for FREE using your %1$sGoogle account, Facebook account%2$s or %1$syour email account%2$s."
msgstr ""

msgid "Connect Now"
msgstr ""

#. translators: variables used to wrap the text in the strong tag.
msgid "You have successfully activated Rank Math. If you find the plugin useful, %1$s feel free to recommend it to your friends or colleagues %2$s."
msgstr ""

msgid "Disconnect Account"
msgstr ""

msgid "Sorry, you are not allowed to deactivate plugins for this site."
msgstr ""

#. translators: plugin name
msgid "You are not allowed to deactivate this plugin: %s."
msgstr ""

#. translators: deactivation link
msgid "Please keep only one SEO plugin active, otherwise, you might lose your rankings and traffic. %s."
msgstr ""

msgid "Click here to Deactivate"
msgstr ""

#. translators: deactivation link
msgid "Please keep only one Sitemap plugin active, otherwise, you might lose your rankings and traffic. %s."
msgstr ""

#. translators: %1$s: general reading settings URL.
msgid "<strong>SEO Notice</strong>: Your site is set to No Index and will not appear in search engines. You can change the Search engine visibility <a href=\"%1$s\">from here</a>."
msgstr ""

#. translators: 1 is plugin name
msgid "Import meta data from the %1$s plugin."
msgstr ""

#. translators: 1 is plugin name
msgid "Import settings and meta data from the %1$s plugin."
msgstr ""

#. translators: 2 is link to Knowledge Base article
msgid "The process may take a few minutes if you have a large number of posts or pages <a href=\"%2$s\" target=\"_blank\">Learn more about the import process here.</a>"
msgstr ""

#. translators: 1 is plugin name
msgid " %1$s plugin will be disabled automatically moving forward to avoid conflicts. <strong>It is thus recommended to import the data you need now.</strong>"
msgstr ""

msgid "None (Click here to set one)"
msgstr ""

msgid "Personal Blog"
msgstr ""

msgid "Community Blog/News Site"
msgstr ""

msgid "Personal Portfolio"
msgstr ""

msgid "Small Business Site"
msgstr ""

msgid "Webshop"
msgstr ""

msgid "Other Personal Website"
msgstr ""

msgid "Other Business Website"
msgstr ""

msgid "Setup Wizard - Rank Math"
msgstr ""

msgid "Connect Google Services"
msgstr ""

msgid "Benefits of Connecting Google Account"
msgstr ""

msgid "Verify site ownership on Google Search Console in a single click"
msgstr ""

msgid "Track page and keyword rankings with the Advanced Analytics module"
msgstr ""

msgid "Easily set up Google Analytics without using another 3rd party plugin"
msgstr ""

msgid "Automatically submit sitemaps to the Google Search Console"
msgstr ""

msgid "Learn more about the benefits of connecting your account here."
msgstr ""

#. Translators: placeholder is the KB link.
#. translators: %s: Link to KB article
msgid "We do not store any of the data from your Google account on our servers, everything is processed & stored on your server. We take your privacy extremely seriously and ensure it is never misused. %s"
msgstr ""

#. Translators: placeholder is the KB link.
#. translators: %s: Link to KB article
msgid "Learn more."
msgstr ""

msgid "Connect Your Rank Math Account"
msgstr ""

msgid "Benefits of Connecting Rank Math Account"
msgstr ""

msgid "Free keyword suggestions when entering a focus keyword"
msgstr ""

msgid "Use our revolutionary SEO Analyzer to scan your website for SEO errors"
msgstr ""

msgid "Reconnect"
msgstr ""

msgid "Disconnect"
msgstr ""

msgid "Test Connections"
msgstr ""

msgid "Search Console"
msgstr ""

msgid "Site"
msgstr ""

msgid "Enable the Index Status tab"
msgstr ""

msgid "Enable this option to show the Index Status tab in the Analytics module."
msgstr ""

msgid "Analytics"
msgstr ""

msgid "Note"
msgstr ""

#. translators: %s: Link to KB article
msgid "Ready to switch to Google Analytics 4? %s"
msgstr ""

msgid "Click here to know how"
msgstr ""

msgid "Property"
msgstr ""

msgid "Data Stream"
msgstr ""

msgid "Install analytics code"
msgstr ""

msgid "Enable this option only if you are not using any other plugin/theme to install Google Analytics code."
msgstr ""

msgid "Anonymize IP addresses"
msgstr ""

msgid "PRO"
msgstr ""

msgid "Self-Hosted Analytics JS File"
msgstr ""

msgid "Exclude Logged-in users"
msgstr ""

msgid "AdSense"
msgstr ""

msgid "Select Account"
msgstr ""

msgid "Google AdSense support is only available in Rank Math Pro's Advanced Analytics module."
msgstr ""

msgid "Email Reports"
msgstr ""

msgid "Receive Analytics reports periodically in email."
msgstr ""

msgid "Learn more about Email Reports."
msgstr ""

#. translators: %s is a Rank Math link.
msgid "Powered by %s"
msgstr ""

msgid "SEO Score"
msgstr ""

msgid "Home"
msgstr ""

#. translators: Archive title
#. translators: placeholder
msgid "Archives for %s"
msgstr ""

#. translators: Search query term
#. translators: placeholder
msgid "Results for %s"
msgstr ""

msgid "404 Error: page not found"
msgstr ""

#. Translators: placeholder is the site title.
msgid "[%s] An update is available for Rank Math"
msgstr ""

msgid "Hello,"
msgstr ""

#. Translators: placeholder is the site URL.
msgid "This is an automated email to let you know that there is an update available for the Rank Math SEO plugin installed on: %s"
msgstr ""

#. Translators: placeholder is the new admin page URL.
msgid "To ensure your site is always on the latest, most up-to-date version of Rank Math - we recommend logging into the admin area to update the plugin as soon as possible: %s"
msgstr ""

msgid "If you have any questions or experience any issues – our support team is at your disposal:"
msgstr ""

msgid "https://support.rankmath.com/"
msgstr ""

msgid "Rank Math Team"
msgstr ""

msgid "Rank Math Free"
msgstr ""

#. Translators: placeholders are the old and new version numbers.
msgid "%1$s: Old %2$s -> New %3$s | Changelog: %4$s"
msgstr ""

#. translators: %s: product tag
msgid "Products tagged &ldquo;%s&rdquo;"
msgstr ""

#. translators: %s expands to the current page number
msgid "Page %s"
msgstr ""

msgid "Search Engine Optimization by Rank Math - https://rankmath.com/"
msgstr ""

msgid "Search Engine Optimization by Rank Math PRO - https://rankmath.com/"
msgstr ""

msgid "Rank Math WordPress SEO plugin"
msgstr ""

msgid "Address:"
msgstr ""

msgid "Hours:"
msgstr ""

msgid "Telephone"
msgstr ""

msgid "Email:"
msgstr ""

msgid "Description:"
msgstr ""

msgid "Page not found"
msgstr ""

msgid "Play icon"
msgstr ""

msgid "GIF icon"
msgstr ""

msgid "Index"
msgstr ""

msgid "Instructs search engines to index and show these pages in the search results."
msgstr ""

msgid "Prevents pages from being indexed and displayed in search engine result pages"
msgstr ""

msgid "No Follow"
msgstr ""

msgid "Prevents search engines from following links on the pages"
msgstr ""

msgid "No Archive"
msgstr ""

msgid "Prevents search engines from showing Cached links for pages"
msgstr ""

msgid "No Image Index"
msgstr ""

msgid "Prevents images on a page from being indexed by Google and other search engines"
msgstr ""

msgid "No Snippet"
msgstr ""

msgid "Prevents a snippet from being shown in the search results"
msgstr ""

msgid "Any"
msgstr ""

msgid "Article"
msgstr ""

msgid "Book"
msgstr ""

msgid "Course"
msgstr ""

msgid "Event"
msgstr ""

msgid "Job Posting"
msgstr ""

msgid "Music"
msgstr ""

msgid "Product"
msgstr ""

msgid "Recipe"
msgstr ""

msgid "Restaurant"
msgstr ""

msgid "Video"
msgstr ""

msgid "Person"
msgstr ""

msgid "Service"
msgstr ""

msgid "Software Application"
msgstr ""

msgid "Review (Unsupported)"
msgstr ""

msgid "301 Permanent Move"
msgstr ""

msgid "302 Temporary Move"
msgstr ""

msgid "307 Temporary Redirect"
msgstr ""

msgid "410 Content Deleted"
msgstr ""

msgid "451 Content Unavailable for Legal Reasons"
msgstr ""

msgid "Exact"
msgstr ""

msgid "Contains"
msgstr ""

msgid "Starts With"
msgstr ""

msgid "End With"
msgstr ""

msgid "Regex"
msgstr ""

msgid "Customer Service"
msgstr ""

msgid "Technical Support"
msgstr ""

msgid "Billing Support"
msgstr ""

msgid "Bill Payment"
msgstr ""

msgid "Sales"
msgstr ""

msgid "Reservations"
msgstr ""

msgid "Credit Card Support"
msgstr ""

msgid "Emergency"
msgstr ""

msgid "Baggage Tracking"
msgstr ""

msgid "Roadside Assistance"
msgstr ""

msgid "Package Tracking"
msgstr ""

msgid "Legal Name"
msgstr ""

msgid "Founding Date"
msgstr ""

msgid "ISO 6523 Code"
msgstr ""

msgid "DUNS"
msgstr ""

msgid "LEI Code"
msgstr ""

msgid "NAICS Code"
msgstr ""

msgid "Global Location Number"
msgstr ""

msgid "VAT ID"
msgstr ""

msgid "Tax ID"
msgstr ""

msgid "Number of Employees"
msgstr ""

msgid "Worldwide"
msgstr ""

msgid "Algeria"
msgstr ""

msgid "Argentina"
msgstr ""

msgid "Armenia"
msgstr ""

msgid "Australia"
msgstr ""

msgid "Austria"
msgstr ""

msgid "Azerbaijan"
msgstr ""

msgid "Bahrain"
msgstr ""

msgid "Bangladesh"
msgstr ""

msgid "Belarus"
msgstr ""

msgid "Belgium"
msgstr ""

msgid "Bolivia, Plurinational State Of"
msgstr ""

msgid "Brazil"
msgstr ""

msgid "Bulgaria"
msgstr ""

msgid "Cambodia"
msgstr ""

msgid "Canada"
msgstr ""

msgid "Chile"
msgstr ""

msgid "Colombia"
msgstr ""

msgid "Costa Rica"
msgstr ""

msgid "Croatia"
msgstr ""

msgid "Cyprus"
msgstr ""

msgid "Czechia"
msgstr ""

msgid "Denmark"
msgstr ""

msgid "Ecuador"
msgstr ""

msgid "Egypt"
msgstr ""

msgid "El Salvador"
msgstr ""

msgid "Estonia"
msgstr ""

msgid "Finland"
msgstr ""

msgid "France"
msgstr ""

msgid "Germany"
msgstr ""

msgid "Ghana"
msgstr ""

msgid "Greece"
msgstr ""

msgid "Guatemala"
msgstr ""

msgid "Hong Kong"
msgstr ""

msgid "Hungary"
msgstr ""

msgid "India"
msgstr ""

msgid "Indonesia"
msgstr ""

msgid "Ireland"
msgstr ""

msgid "Israel"
msgstr ""

msgid "Italy"
msgstr ""

msgid "Japan"
msgstr ""

msgid "Jordan"
msgstr ""

msgid "Kazakhstan"
msgstr ""

msgid "Kenya"
msgstr ""

msgid "Korea, Republic Of"
msgstr ""

msgid "Latvia"
msgstr ""

msgid "Lithuania"
msgstr ""

msgid "Macedonia, The Former Yugoslav Republic Of"
msgstr ""

msgid "Malaysia"
msgstr ""

msgid "Malta"
msgstr ""

msgid "Mexico"
msgstr ""

msgid "Morocco"
msgstr ""

msgid "Myanmar"
msgstr ""

msgid "Netherlands"
msgstr ""

msgid "New Zealand"
msgstr ""

msgid "Nicaragua"
msgstr ""

msgid "Nigeria"
msgstr ""

msgid "Norway"
msgstr ""

msgid "Pakistan"
msgstr ""

msgid "Paraguay"
msgstr ""

msgid "Peru"
msgstr ""

msgid "Philippines"
msgstr ""

msgid "Poland"
msgstr ""

msgid "Portugal"
msgstr ""

msgid "Romania"
msgstr ""

msgid "Russian Federation"
msgstr ""

msgid "Saudi Arabia"
msgstr ""

msgid "Senegal"
msgstr ""

msgid "Serbia"
msgstr ""

msgid "Singapore"
msgstr ""

msgid "Slovakia"
msgstr ""

msgid "Slovenia"
msgstr ""

msgid "South Africa"
msgstr ""

msgid "Spain"
msgstr ""

msgid "Sri Lanka"
msgstr ""

msgid "Sweden"
msgstr ""

msgid "Switzerland"
msgstr ""

msgid "Taiwan"
msgstr ""

msgid "Thailand"
msgstr ""

msgid "Tunisia"
msgstr ""

msgid "Turkey"
msgstr ""

msgid "Ukraine"
msgstr ""

msgid "United Arab Emirates"
msgstr ""

msgid "United Kingdom"
msgstr ""

msgid "United States Of America"
msgstr ""

msgid "Uruguay"
msgstr ""

msgid "Venezuela, Bolivarian Republic Of"
msgstr ""

msgid "Viet Nam"
msgstr ""

msgid "Please connect your account to use the Content AI."
msgstr ""

msgid "Please update the Rank Math SEO plugin to the latest version to use this feature."
msgstr ""

msgid "This feature is only available for Content AI subscribers."
msgstr ""

msgid "Oops! Too many requests in a short time. Please try again after some time."
msgstr ""

msgid "You've used up all available credits for this domain."
msgstr ""

msgid "You've used up all available credits from the connected account."
msgstr ""

msgid "Please revise the entered values in the fields as they are not secure. Make the required adjustments and try again."
msgstr ""

msgid "The output was stopped as it was identified as potentially unsafe by the content filter."
msgstr ""

msgid "Could not generate. Please try again later."
msgstr ""

msgid "Invalid API key. Please check your API key or reconnect the site and try again."
msgstr ""

msgid "The input provided is invalid. Please check the format and try again."
msgstr ""

msgid "The service is temporarily unavailable. Please try again later."
msgstr ""

msgid "Unauthorized request. The client credentials are invalid."
msgstr ""

msgid "No results found for the given query. Please modify your request and try again"
msgstr ""

msgid "Insufficient results to complete the request. Please refine your query or reduce the requirements."
msgstr ""

msgid "User wallet not found."
msgstr ""

msgid "WooCommerce Product"
msgstr ""

msgid "EDD Product"
msgstr ""

msgid "Items per page"
msgstr ""

msgid "404 Monitor"
msgstr ""

msgid "Records the URLs on which visitors & search engines run into 404 Errors. You can also turn on Redirections to redirect the error causing URLs to other URLs."
msgstr ""

msgid "Dominate the search results for the local audiences by optimizing your website for Local SEO and it also helps you to aquire the Knowledge Graph."
msgstr ""

msgid "Redirections"
msgstr ""

msgid "Redirect non-existent content easily with 301 and 302 status code. This can help improve your site ranking. Also supports many other response codes."
msgstr ""

msgid "Schema (Structured Data)"
msgstr ""

msgid "Enable support for the structured data, which adds Schema code in your website, resulting in rich search results, better CTR and more traffic."
msgstr ""

msgid "Sitemap"
msgstr ""

msgid "Enable Rank Math's sitemap feature, which helps search engines intelligently crawl your website's content. It also supports hreflang tag."
msgstr ""

msgid "Link Counter"
msgstr ""

msgid "Counts the total number of internal, external links, to and from links inside your posts. You can also see the same count in the Posts List Page."
msgstr ""

msgid "Image SEO"
msgstr ""

msgid "Advanced Image SEO options to supercharge your website. Automate the task of adding the ALT and Title tags to your images on the fly."
msgstr ""

msgid "Instant Indexing"
msgstr ""

#. Translators: placeholder is "IndexNow API".
msgid "Directly notify search engines like Bing & Yandex using the %s when pages are added, updated and removed, or submit URLs manually."
msgstr ""

#. Translators: placeholder is "IndexNow API".
msgid "IndexNow API"
msgstr ""

msgid "Content AI"
msgstr ""

msgid "Get sophisticated AI suggestions for related Keywords, Questions & Links to include in the SEO meta & Content Area. Supports 80+ Countries."
msgstr ""

msgid "News Sitemap"
msgstr ""

msgid "Create a News Sitemap for your news-related content. You only need a News sitemap if you plan on posting news-related content on your website."
msgstr ""

msgid "This module is available in the PRO version."
msgstr ""

msgid "Video Sitemap"
msgstr ""

msgid "For your video content, a Video Sitemap is a recommended step for better rankings and inclusion in the Video search."
msgstr ""

msgid "Podcast"
msgstr ""

msgid "Make your podcasts discoverable via Google Podcasts, Apple Podcasts, and similar services with Podcast RSS feed and Schema Markup generated by Rank Math."
msgstr ""

msgid "Role Manager"
msgstr ""

msgid "The Role Manager allows you to use WordPress roles to control which of your site users can have edit or view access to Rank Math's settings."
msgstr ""

msgid "Connect Rank Math with Google Search Console to see the most important information from Google directly in your WordPress dashboard."
msgstr ""

msgid "SEO Analyzer"
msgstr ""

msgid "Let Rank Math analyze your website and your website's content using 28+ different tests to provide tailor-made SEO Analysis to you."
msgstr ""

msgid "Robots Txt"
msgstr ""

msgid "Version Control"
msgstr ""

msgid "Database Tools"
msgstr ""

msgid "Status"
msgstr ""

msgid "AMP"
msgstr ""

#. translators: Link to AMP plugin
msgid "Install %s to make Rank Math work with Accelerated Mobile Pages. Rank Math automatically adds required meta tags in all the AMP pages."
msgstr ""

msgid "AMP plugin"
msgstr ""

msgid "bbPress"
msgstr ""

msgid "Add proper Meta tags to your bbPress forum posts, categories, profiles, etc. Get more options to take control of what search engines see and how they see it."
msgstr ""

msgid "Please activate bbPress plugin to use this module."
msgstr ""

msgid "BuddyPress"
msgstr ""

msgid "Enable the BuddyPress module for Rank Math SEO to make your BuddyPress forum SEO friendly by adding proper meta tags to all forum pages."
msgstr ""

msgid "Please activate BuddyPress plugin to use this module."
msgstr ""

msgid "WooCommerce"
msgstr ""

msgid "Optimize WooCommerce Pages for Search Engines by adding required metadata and Product Schema which will make your site stand out in the SERPs."
msgstr ""

msgid "Please activate WooCommerce plugin to use this module."
msgstr ""

msgid "ACF"
msgstr ""

msgid "ACF support helps Rank Math SEO read and analyze content written in the Advanced Custom Fields. If your theme uses ACF, you should enable this option."
msgstr ""

msgid "Please activate ACF plugin to use this module."
msgstr ""

msgid "Google Web Stories"
msgstr ""

msgid "Make any Story created with the Web Stories WordPress plugin SEO-Ready with automatic support for Schema and Meta tags."
msgstr ""

msgid "Please activate Web Stories plugin to use this module."
msgstr ""

msgid "You cant access this page."
msgstr ""

msgid "Free"
msgstr ""

msgid "NEW!"
msgstr ""

msgid "More powerful options are available in the PRO version."
msgstr ""

msgid "PRO options are enabled."
msgstr ""

msgid "Take SEO to the Next Level!"
msgstr ""

msgid "Unlimited personal websites"
msgstr ""

msgid "Free 15 Content AI Credits"
msgstr ""

msgid "Track 500 Keywords"
msgstr ""

msgid "24/7 Support"
msgstr ""

msgid "Buy"
msgstr ""

msgid "Settings"
msgstr ""

#. translators: delete counter
msgid "%d log(s) deleted."
msgstr ""

#. translators: delete counter
msgid "Log cleared - %d items deleted."
msgstr ""

msgid "Overview"
msgstr ""

msgid "Screen Content"
msgstr ""

msgid "Available Actions"
msgstr ""

msgid "Bulk Actions"
msgstr ""

msgid "Are you sure you wish to delete all 404 error logs?"
msgstr ""

#. translators: 1. Link to KB article 2. Link to redirection setting scree
msgid "Monitor broken pages that ruin user-experience and affect SEO. %s."
msgstr ""

msgid "View Report"
msgstr ""

msgid "Log Count"
msgstr ""

msgid "Total number of 404 pages opened by the users."
msgstr ""

msgid "URL Hits"
msgstr ""

msgid "Total number visits received on all the 404 pages."
msgstr ""

msgid "Review 404 errors on your site"
msgstr ""

msgid "No valid id found."
msgstr ""

msgid "Log item successfully deleted."
msgstr ""

msgid "The 404 error log is empty."
msgstr ""

msgid "Clear Log"
msgstr ""

msgid "View"
msgstr ""

msgid "Delete"
msgstr ""

msgid "View Redirection"
msgstr ""

msgid "URI"
msgstr ""

msgid "Referer"
msgstr ""

msgid "User-Agent"
msgstr ""

msgid "Hits"
msgstr ""

msgid "Access Time"
msgstr ""

msgid "Hovering over a row in the list will display action links that allow you to manage the item. You can perform the following actions:"
msgstr ""

msgid "<strong>View Details</strong> shows details about the 404 requests."
msgstr ""

msgid "<strong>Redirect</strong> takes you to the Redirections manager to redirect the 404 URL."
msgstr ""

msgid "<strong>Delete</strong> permanently removes the item from the list."
msgstr ""

msgid "You can also redirect or delete multiple items at once. Selecting multiple items to redirect allows you to redirect them to a single URL."
msgstr ""

msgid "With the 404 monitor you can see where users and search engines are unable to find your content."
msgstr ""

msgid "Knowledge Base Articles:"
msgstr ""

msgid "404 Monitor Settings"
msgstr ""

msgid "Fix 404 Errors"
msgstr ""

msgid "You can customize the display of this screen's contents in a number of ways:"
msgstr ""

msgid "You can hide/display columns based on your needs."
msgstr ""

msgid "You can decide how many items to list per screen using the Screen Options tab."
msgstr ""

msgid "You can search items using the search form at the top."
msgstr ""

msgid "You can reorder the list by clicking on the column headings. "
msgstr ""

msgid "Search"
msgstr ""

msgid "If you have hundreds of 404 errors, your error log might increase quickly. Only choose this option if you have a very few 404s and are unable to replicate the 404 error on a particular URL from your end."
msgstr ""

msgid "Mode"
msgstr ""

msgid "The Simple mode only logs URI and access time, while the Advanced mode creates detailed logs including additional information such as the Referer URL."
msgstr ""

msgid "Simple"
msgstr ""

msgid "Advanced"
msgstr ""

msgid "Log Limit"
msgstr ""

msgid "Sets the max number of rows in a log. Set to 0 to disable the limit."
msgstr ""

msgid "Exclude Paths"
msgstr ""

msgid "Enter URIs or keywords you wish to prevent from getting logged by the 404 monitor."
msgstr ""

msgid "Add another"
msgstr ""

msgid "Remove"
msgstr ""

msgid "Ignore Query Parameters"
msgstr ""

msgid "Turn ON to ignore all query parameters (the part after a question mark in a URL) when logging 404 errors."
msgstr ""

msgid "Data import will not work for this service as sufficient permissions are not given."
msgstr ""

msgid "Data fetching cancelled."
msgstr ""

msgid "Google oAuth is not authorized."
msgstr ""

msgid "Data fetching started in the background."
msgstr ""

msgid "Not a valid settings founds to delete cache."
msgstr ""

msgid "Last 30 Days"
msgstr ""

msgid "Review analytics and sitemaps"
msgstr ""

msgid "Analytics cache cleared."
msgstr ""

msgid "Post re-index in progress."
msgstr ""

#. translators: %1$d: number of changes, %2$s: new collation.
msgid "%1$d collation changed to %2$s."
msgid_plural "%1$d collations changed to %2$s."
msgstr[0] ""
msgstr[1] ""

msgid "No collation mismatch to fix."
msgstr ""

msgid "Search Traffic"
msgstr ""

msgid "This is the number of pageviews carried out by visitors from Search Engines."
msgstr ""

msgid "Total Impressions"
msgstr ""

msgid "How many times your site showed up in the search results."
msgstr ""

msgid "Total Clicks"
msgstr ""

msgid "This is the number of pageviews carried out by visitors from Google."
msgstr ""

msgid "Total Keywords"
msgstr ""

msgid "Total number of keywords your site ranks for within top 100 positions."
msgstr ""

msgid "Average Position"
msgstr ""

msgid "Average position of all the keywords ranking within top 100 positions."
msgstr ""

#. translators: Auth URL
msgid "It seems like the connection with your Google account & Rank Math needs to be made again. <a href=\"%s\" class=\"rank-math-reconnect-google\">Please click here.</a>"
msgstr ""

msgid "Rank Math is importing latest data from connected Google Services, %1$s remaining."
msgstr ""

msgid "Cancel Fetch"
msgstr ""

#. translators: constant value
msgid "Loopback requests to %s are blocked. This may prevent scheduled tasks from running. Please check your server configuration."
msgstr ""

#. translators: constant value
msgid "WordPress's internal cron system is disabled via %s. Please ensure a real cron job is set up, otherwise scheduled features like Analytics may not work correctly."
msgstr ""

#. translators: %s: amount of time
msgid "%s year"
msgid_plural "%s years"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s month"
msgid_plural "%s months"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s week"
msgid_plural "%s weeks"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s day"
msgid_plural "%s days"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s hour"
msgid_plural "%s hours"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: amount of time
msgid "%s second"
msgid_plural "%s seconds"
msgstr[0] ""
msgstr[1] ""

msgid "Now!"
msgstr ""

msgid "Are you sure you want to do this?"
msgstr ""

msgid "You are about to delete all the previously imported data."
msgstr ""

msgid "You are about to delete your 90 days cache."
msgstr ""

msgid "Are you sure you want to disconnect Google services from your site?"
msgstr ""

msgid "Cache deleted."
msgstr ""

#. translators: Link to kb article
msgid "See your Google Search Console, Analytics and AdSense data without leaving your WP dashboard. %s."
msgstr ""

#. Translators: placeholder is "rankmath.com" as a link.
msgid "This email was sent to you as a registered member of %s."
msgstr ""

#. Translators: placeholder is "click here" as a link.
msgid "To update your email preferences, %s."
msgstr ""

#. Translators: placeholder is "click here" as a link.
msgid "click here"
msgstr ""

#. Translators: placeholder is the site URL.
msgid "Rank Math [SEO Report] - %s"
msgstr ""

msgid "Sorry, no post found for given id."
msgstr ""

msgid "The Google Analytics Console request failed."
msgstr ""

msgid "The Google Search Console request failed."
msgstr ""

msgid "Given"
msgstr ""

msgid "Not Given"
msgstr ""

msgid "Warning:"
msgstr ""

#. translators: %s is the reconnect link.
msgid "You have not given the permission to fetch this data. Please <a href=\"%s\">reconnect</a> with all required permissions."
msgstr ""

#. translators: reconnect link
msgid "There is a problem with the Google auth token. Please <a href=\"%1$s\" class=\"button button-link rank-math-reconnect-google\">reconnect your app</a>"
msgstr ""

msgid "Bad request. Please check the code."
msgstr ""

msgid "Unknown error, call get_response() to find out what happened."
msgstr ""

msgid "Post ID."
msgstr ""

msgid "Post Type."
msgstr ""

msgid "Page number."
msgstr ""

msgid "Results per page."
msgstr ""

msgid "Order by."
msgstr ""

msgid "Order."
msgstr ""

msgid "Search."
msgstr ""

msgid "User preferences."
msgstr ""

msgid "Filter."
msgstr ""

msgid "Filter type."
msgstr ""

msgid "Toggle bar."
msgstr ""

msgid "Hide."
msgstr ""

msgid "Sorry, no preference found."
msgstr ""

msgid "Sorry, no post id found."
msgstr ""

msgid "Rank Math PRO"
msgstr ""

msgid "SEO Report of Your Website"
msgstr ""

msgid "FULL REPORT"
msgstr ""

msgid "External Link Icon"
msgstr ""

msgid "Uh-oh"
msgstr ""

msgid "It seems that there are no stats to show right now."
msgstr ""

#. Translators: placeholders are anchor opening and closing tags.
msgid "If you can see the site data in your Search Console and Analytics accounts, but not here, then %1$s try reconnecting your account %2$s and make sure that the correct properties are selected in the %1$s Analytics Settings%2$s."
msgstr ""

#. Translators: don't translate the variable names between the #hashes#.
msgid "Last ###PERIOD_DAYS### Days"
msgstr ""

msgid "Top 3 Positions"
msgstr ""

msgid "4-10 Positions"
msgstr ""

msgid "11-50 Positions"
msgstr ""

msgid "Data Chart"
msgstr ""

#. translators: number of days
msgid "Storage Days: %s"
msgstr ""

#. translators: number of rows
msgid "Data Rows: %s"
msgstr ""

#. translators: database size
msgid "Size: %s"
msgstr ""

msgid "Next data fetch on %s"
msgstr ""

msgid "Delete data"
msgstr ""

msgid "Update data manually"
msgstr ""

msgid "Fetching in Progress"
msgstr ""

msgid "Cancel Fetching"
msgstr ""

#. Translators: placeholder is a link to rankmath.com, with "free version" as the anchor text.
msgid "Enter the number of days to keep Analytics data in your database. The maximum allowed days are 90 in the %s. Though, 2x data will be stored in the DB for calculating the difference properly."
msgstr ""

#. Translators: placeholder is a link to rankmath.com, with "free version" as the anchor text.
msgid "free version"
msgstr ""

msgid "Analytics Database"
msgstr ""

msgid "Frontend Stats Bar"
msgstr ""

msgid "Enable this option to show Analytics Stats on the front just after the admin bar."
msgstr ""

#. Translators: Placeholders are the opening and closing tag for the link.
msgid "Receive periodic SEO Performance reports via email. Once enabled and options are saved, you can see %1$s the preview here%2$s."
msgstr ""

msgid "Turn on email reports."
msgstr ""

msgid "Email Frequency"
msgstr ""

msgid "Email report frequency."
msgstr ""

msgid "Every 30 days"
msgstr ""

msgid "BuddyPress:"
msgstr ""

msgid "Groups"
msgstr ""

msgid "This tab contains SEO options for BuddyPress Group pages."
msgstr ""

msgid "Group name."
msgstr ""

msgid "Group name of the current group"
msgstr ""

msgid "Group Description."
msgstr ""

msgid "Group description of the current group"
msgstr ""

msgid "Group Title"
msgstr ""

msgid "Title tag for groups"
msgstr ""

msgid "Group Description"
msgstr ""

msgid "BuddyPress group description"
msgstr ""

msgid "Group Robots Meta"
msgstr ""

msgid "Select custom robots meta for Group archive pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr ""

msgid "Default"
msgstr ""

msgid "Custom"
msgstr ""

msgid "Custom values for robots meta tag on groups page."
msgstr ""

msgid "Group Advanced Robots Meta"
msgstr ""

#. translators: Link to kb article
msgid "Get sophisticated AI suggestions for related Keywords, Questions & Links to include in the SEO meta & Content Area. %s."
msgstr ""

msgid "Content AI Plan"
msgstr ""

msgid "Content AI Credits"
msgstr ""

msgid "Content AI Refresh Date"
msgstr ""

msgid "&#8595; Rank Math Content AI"
msgstr ""

msgid "Write SEO Title with AI"
msgstr ""

msgid "Write SEO Description with AI"
msgstr ""

msgid "Write SEO Title & Description with AI"
msgstr ""

msgid "Write Image Alt Text with AI"
msgstr ""

msgid "Another bulk editing process is already running. Please try again later after the existing process is complete."
msgstr ""

msgid "Cancel Content AI Bulk Editing Process"
msgstr ""

#. Translators: placeholders are the number of posts that were processed.
msgid "Terminate the ongoing Content AI Bulk Editing Process to halt any pending modifications and revert to the previous state. The bulk metadata has been generated for %1$d out of %1$d posts so far."
msgstr ""

msgid "Terminate"
msgstr ""

msgid "Bulk Editing Process Successfully Cancelled"
msgstr ""

msgid "Bulk editing SEO meta started. It might take few minutes to complete the process."
msgstr ""

#. Translators: placeholder is the number of modified items.
msgid "SEO meta successfully updated in %d item."
msgid_plural "SEO meta successfully updated in %d items."
msgstr[0] ""
msgstr[1] ""

#. Translators: placeholder is the number of modified posts.
msgid "SEO meta successfully updated in %d posts. The process was stopped as you have used all the credits on your site."
msgstr ""

msgid "Bulk image alt generation started. It might take few minutes to complete the process."
msgstr ""

#. Translators: placeholder is the number of modified posts.
msgid "Image alt attributes successfully updated in %d post."
msgid_plural "Image alt attributes successfully updated in %d posts."
msgstr[0] ""
msgstr[1] ""

msgid "Bulk image alt generation failed."
msgstr ""

#. Translators: placeholder is the number of modified posts.
msgid "Image alt attributes successfully updated in %d posts. The process was stopped as you have used all the credits on your site."
msgstr ""

msgid "Credits Remaining: "
msgstr ""

msgid "New!"
msgstr ""

#. Translators: placeholder is the new label.
msgid "Content AI %s"
msgstr ""

msgid "AI Tools"
msgstr ""

msgid "Content AI Tools"
msgstr ""

msgid "Content Editor"
msgstr ""

msgid "Content AI Editor"
msgstr ""

msgid "Chat"
msgstr ""

msgid "Content AI Chat"
msgstr ""

msgid "History"
msgstr ""

msgid "Content AI History"
msgstr ""

msgid "The content of the new post."
msgstr ""

msgid "The title of the new post."
msgstr ""

msgid "An array of AI-generated and existing outputs to be saved."
msgstr ""

msgid "The API endpoint for which the output was generated."
msgstr ""

msgid "Indicates if the request was for the Chat endpoint."
msgstr ""

msgid "The parameters used to generate the AI output."
msgstr ""

msgid "Credit usage details returned by the API."
msgstr ""

msgid "Indicates if the request to delete the output was for the Chat endpoint."
msgstr ""

msgid "The output index to delete, applicable only to the Chat endpoint."
msgstr ""

msgid "The selected prompt to be updated in the recent prompts."
msgstr ""

msgid "The prompt data to be saved in the database."
msgstr ""

msgid "A list of prompts received from the API to be saved in the database."
msgstr ""

msgid "Content AI plan to update in the Database."
msgstr ""

msgid "Content AI reset date to update in the Database"
msgstr ""

msgid "List of attachment IDs for which to generate alt text."
msgstr ""

msgid "Sorry, only authenticated users can research the keyword."
msgstr ""

msgid "Content AI is not enabled on this Post type."
msgstr ""

msgid "The keyword to be researched."
msgstr ""

msgid "The country for which the keyword should be researched."
msgstr ""

msgid "The ID of the post initiating the keyword research request."
msgstr ""

msgid "If true, forces a fresh research request."
msgstr ""

msgid "No data found for the researched keyword."
msgstr ""

#. Translators: link to the update page.
msgid "There is a new version of Content AI available! %s the Rank Math SEO plugin to use this feature."
msgstr ""

msgid "Please update"
msgstr ""

msgid "This feature is not available on the localhost."
msgstr ""

msgid "You have used all the free credits which are allowed to this domain."
msgstr ""

msgid "Gain Access to 40+ Advanced AI Tools."
msgstr ""

msgid "Experience the Revolutionary AI-Powered Content Editor."
msgstr ""

msgid "Engage with RankBot, Our AI Chatbot, For SEO Advice."
msgstr ""

msgid "Escape the Writer's Block Using AI to Write Inside WordPress."
msgstr ""

msgid "Default Country"
msgstr ""

msgid "Content AI tailors keyword research to the target country for highly relevant suggestions. You can override this in individual posts/pages/CPTs."
msgstr ""

msgid "Default Tone"
msgstr ""

msgid "This feature enables the default primary tone or writing style that characterizes your content. You can override this in individual tools."
msgstr ""

msgid "Analytical"
msgstr ""

msgid "Argumentative"
msgstr ""

msgid "Casual"
msgstr ""

msgid "Conversational"
msgstr ""

msgid "Creative"
msgstr ""

msgid "Descriptive"
msgstr ""

msgid "Emotional"
msgstr ""

msgid "Empathetic"
msgstr ""

msgid "Expository"
msgstr ""

msgid "Factual"
msgstr ""

msgid "Formal"
msgstr ""

msgid "Friendly"
msgstr ""

msgid "Humorous"
msgstr ""

msgid "Informal"
msgstr ""

msgid "Journalese"
msgstr ""

msgid "Narrative"
msgstr ""

msgid "Objective"
msgstr ""

msgid "Opinionated"
msgstr ""

msgid "Persuasive"
msgstr ""

msgid "Poetic"
msgstr ""

msgid "Satirical"
msgstr ""

msgid "Story-telling"
msgstr ""

msgid "Subjective"
msgstr ""

msgid "Technical"
msgstr ""

msgid "Default Audience"
msgstr ""

msgid "This option lets you set the default audience that usually reads your content. You can override this in individual tools."
msgstr ""

msgid "Activists"
msgstr ""

msgid "Artists"
msgstr ""

msgid "Bargain Hunters"
msgstr ""

msgid "Bloggers"
msgstr ""

msgid "Business Owners"
msgstr ""

msgid "Collectors"
msgstr ""

msgid "Cooks"
msgstr ""

msgid "Crafters"
msgstr ""

msgid "Dancers"
msgstr ""

msgid "DIYers"
msgstr ""

msgid "Designers"
msgstr ""

msgid "Educators"
msgstr ""

msgid "Engineers"
msgstr ""

msgid "Entrepreneurs"
msgstr ""

msgid "Environmentalists"
msgstr ""

msgid "Fashionistas"
msgstr ""

msgid "Fitness Enthusiasts"
msgstr ""

msgid "Foodies"
msgstr ""

msgid "Gaming Enthusiasts"
msgstr ""

msgid "Gardeners"
msgstr ""

msgid "General Audience"
msgstr ""

msgid "Health Enthusiasts"
msgstr ""

msgid "Healthcare Professionals"
msgstr ""

msgid "Indoor Hobbyists"
msgstr ""

msgid "Investors"
msgstr ""

msgid "Job Seekers"
msgstr ""

msgid "Movie Buffs"
msgstr ""

msgid "Musicians"
msgstr ""

msgid "Outdoor Enthusiasts"
msgstr ""

msgid "Parents"
msgstr ""

msgid "Pet Owners"
msgstr ""

msgid "Photographers"
msgstr ""

msgid "Podcast Listeners"
msgstr ""

msgid "Professionals"
msgstr ""

msgid "Retirees"
msgstr ""

msgid "Russian"
msgstr ""

msgid "Seniors"
msgstr ""

msgid "Social Media Users"
msgstr ""

msgid "Sports Fans"
msgstr ""

msgid "Students"
msgstr ""

msgid "Tech Enthusiasts"
msgstr ""

msgid "Travelers"
msgstr ""

msgid "TV Enthusiasts"
msgstr ""

msgid "Video Creators"
msgstr ""

msgid "Writers"
msgstr ""

msgid "Default Language"
msgstr ""

msgid "This option lets you set the default language for content generated using Content AI. You can override this in individual tools."
msgstr ""

msgid "US English"
msgstr ""

msgid "UK English"
msgstr ""

msgid "Arabic"
msgstr ""

msgid "Bulgarian"
msgstr ""

msgid "Chinese"
msgstr ""

msgid "Czech"
msgstr ""

msgid "Danish"
msgstr ""

msgid "Dutch"
msgstr ""

msgid "Estonian"
msgstr ""

msgid "Finnish"
msgstr ""

msgid "French"
msgstr ""

msgid "German"
msgstr ""

msgid "Greek"
msgstr ""

msgid "Hebrew"
msgstr ""

msgid "Hungarian"
msgstr ""

msgid "Indonesian"
msgstr ""

msgid "Italian"
msgstr ""

msgid "Japanese"
msgstr ""

msgid "Korean"
msgstr ""

msgid "Latvian"
msgstr ""

msgid "Lithuanian"
msgstr ""

msgid "Norwegian"
msgstr ""

msgid "Polish"
msgstr ""

msgid "Portuguese"
msgstr ""

msgid "Romanian"
msgstr ""

msgid "Slovak"
msgstr ""

msgid "Slovenian"
msgstr ""

msgid "Spanish"
msgstr ""

msgid "Swedish"
msgstr ""

msgid "Turkish"
msgstr ""

msgid "Select Post Type"
msgstr ""

msgid "Choose the type of posts/pages/CPTs where you want to use Content AI."
msgstr ""

msgid "Click to refresh the available credits."
msgstr ""

#. translators: 1. Credits left 2. Buy more credits link
msgid "%1$s credits left this month. Credits will renew on %2$s or you can upgrade to get more credits %3$s."
msgstr ""

#. translators: 1. Credits left 2. Buy more credits link
msgid "here"
msgstr ""

#. Translators: placeholder is the number of modified posts.
msgid "Blocks successfully converted in %d post."
msgid_plural "Blocks successfully converted in %d posts."
msgstr[0] ""
msgstr[1] ""

msgid "No Rank Math transients found."
msgstr ""

#. Translators: placeholder is the number of transients deleted.
msgid "%d Rank Math transient cleared."
msgid_plural "%d Rank Math transients cleared."
msgstr[0] ""
msgstr[1] ""

msgid "SEO Analyzer data has already been cleared."
msgstr ""

msgid "SEO Analyzer data successfully deleted."
msgstr ""

msgid "No Internal Links data found."
msgstr ""

msgid "Internal Links successfully deleted."
msgstr ""

msgid "No 404 log data found."
msgstr ""

msgid "404 Log successfully deleted."
msgstr ""

msgid "No Redirections found."
msgstr ""

msgid "Redirection rules successfully deleted."
msgstr ""

msgid "Table re-creation started. It might take a couple of minutes."
msgstr ""

msgid "No posts found to convert."
msgstr ""

msgid "Conversion started. A success message will be shown here once the process completes. You can close this page."
msgstr ""

msgid "Flush SEO Analyzer Data"
msgstr ""

msgid "Need a clean slate or not able to run the SEO Analyzer tool? Flushing the analysis data might fix the issue. Flushing SEO Analyzer data is entirely safe and doesn't remove any critical data from your website."
msgstr ""

msgid "Clear SEO Analyzer"
msgstr ""

msgid "Remove Rank Math Transients"
msgstr ""

msgid "If you see any issue while using Rank Math or one of its options - clearing the Rank Math transients fixes the problem in most cases. Deleting transients does not delete ANY data added using Rank Math."
msgstr ""

msgid "Remove transients"
msgstr ""

msgid "Clear 404 Log"
msgstr ""

msgid "Is the 404 error log getting out of hand? Use this option to clear ALL 404 logs generated by your website in the Rank Math 404 Monitor."
msgstr ""

msgid "Are you sure you want to delete the 404 log? This action is irreversible."
msgstr ""

msgid "Re-create Missing Database Tables"
msgstr ""

msgid "Check if required tables exist and create them if not."
msgstr ""

msgid "Re-create Tables"
msgstr ""

msgid "Fix Analytics table collations"
msgstr ""

msgid "In some cases, the Analytics database tables or columns don't match with each other, which can cause database errors. This tool can fix that issue."
msgstr ""

msgid "Fix Collations"
msgstr ""

msgid "Yoast Block Converter"
msgstr ""

msgid "Convert FAQ, HowTo, & Table of Contents Blocks created using Yoast. Use this option to easily move your previous blocks into Rank Math."
msgstr ""

msgid "Are you sure you want to convert Yoast blocks into Rank Math blocks? This action is irreversible."
msgstr ""

msgid "Convert Blocks"
msgstr ""

msgid "AIOSEO Block Converter"
msgstr ""

msgid "Convert TOC block created using AIOSEO. Use this option to easily move your previous blocks into Rank Math."
msgstr ""

msgid "Are you sure you want to convert AIOSEO blocks into Rank Math blocks? This action is irreversible."
msgstr ""

msgid "Delete Internal Links Data"
msgstr ""

msgid "In some instances, the internal links data might show an inflated number or no number at all. Deleting the internal links data might fix the issue."
msgstr ""

msgid "Are you sure you want to delete Internal Links Data? This action is irreversible."
msgstr ""

msgid "Delete Internal Links"
msgstr ""

msgid "Delete Redirections Rules"
msgstr ""

msgid "Getting a redirection loop or need a fresh start? Delete all the redirections using this tool. Note: This process is irreversible and will delete ALL your redirection rules."
msgstr ""

msgid "Are you sure you want to delete all the Redirection Rules? This action is irreversible."
msgstr ""

msgid "Delete Redirections"
msgstr ""

msgid "Update SEO Scores"
msgstr ""

msgid "This tool will calculate the SEO score for the posts/pages that have a Focus Keyword set. Note: This process may take some time and the browser tab must be kept open while it is running."
msgstr ""

msgid "Recalculate Scores"
msgstr ""

msgid "Purge Analytics Cache"
msgstr ""

msgid "Clear analytics cache to re-calculate all the stats again."
msgstr ""

msgid "Clear Cache"
msgstr ""

msgid "Rebuild Index for Analytics"
msgstr ""

msgid "Missing some posts/pages in the Analytics data? Clear the index and build a new one for more accurate stats."
msgstr ""

msgid "Rebuild Index"
msgstr ""

msgid "Images"
msgstr ""

#. translators: Link to kb article
msgid "SEO options related to featured images and media appearing in your post content. %s."
msgstr ""

msgid "Add missing ALT attributes"
msgstr ""

msgid "Add <code>alt</code> attributes for <code>images</code> without <code>alt</code> attributes automatically. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr ""

msgid "Alt attribute format"
msgstr ""

msgid "Format used for the new <code>alt</code> attribute values."
msgstr ""

msgid "Add missing TITLE attributes"
msgstr ""

msgid "Add <code>TITLE</code> attribute for all <code>images</code> without a <code>TITLE</code> attribute automatically. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr ""

msgid "Title attribute format"
msgstr ""

msgid "Format used for the new <code>title</code> attribute values."
msgstr ""

msgid "Unknown error."
msgstr ""

msgid "Invalid request."
msgstr ""

msgid "Invalid API key."
msgstr ""

msgid "Invalid URL."
msgstr ""

msgid "Too many requests."
msgstr ""

msgid "Internal server error."
msgstr ""

msgid "Instant Indexing: Submit Pages"
msgstr ""

msgid "Instant Indexing: Submit Page"
msgstr ""

msgid "Submit URLs"
msgstr ""

msgid "Send URLs directly to the IndexNow API."
msgstr ""

#. translators: Link to kb article
msgid "Instant Indexing module settings. %s."
msgstr ""

msgid "The last 100 IndexNow API requests."
msgstr ""

msgid "An error occurred while submitting the URL."
msgstr ""

msgid "Error: could not clear history."
msgstr ""

msgid "Error: could not get history."
msgstr ""

msgid "No submissions yet."
msgstr ""

msgid "Error submitting page to IndexNow."
msgstr ""

#. translators: %s: Number of pages submitted.
msgid "%s page submitted to IndexNow."
msgid_plural "%s pages submitted to IndexNow."
msgstr[0] ""
msgstr[1] ""

msgid "The list of urls to submit to the Instant Indexing API."
msgstr ""

msgid "Filter log by type."
msgstr ""

msgid "Clear log by type."
msgstr ""

msgid "No URLs provided."
msgstr ""

msgid "Invalid URLs provided."
msgstr ""

msgid "Failed to submit URLs. See details in the History tab."
msgstr ""

#. Translators: %s is the number of URLs submitted.
msgid "Successfully submitted %s URL."
msgid_plural "Successfully submitted %s URLs."
msgstr[0] ""
msgstr[1] ""

#. Translators: placeholder is human-readable time, e.g. "1 hour".
msgid "%s ago"
msgstr ""

msgid "Insert URLs to send to the IndexNow API (one per line, up to 10,000):"
msgstr ""

msgctxt "URL slug placeholder"
msgid "hello-world"
msgstr ""

msgid "Clear History"
msgstr ""

msgid "All"
msgstr ""

msgid "Manual"
msgstr ""

msgid "Auto"
msgstr ""

msgid "Time"
msgstr ""

msgid "URL"
msgstr ""

msgid "Response"
msgstr ""

msgid "Response Code Help"
msgstr ""

msgid "Response Code"
msgstr ""

msgid "Response Message"
msgstr ""

msgid "Reasons"
msgstr ""

msgid "OK"
msgstr ""

msgid "The URL was successfully submitted to the IndexNow API."
msgstr ""

msgid "Accepted"
msgstr ""

msgid "The URL was successfully submitted to the IndexNow API, but the API key will be checked later."
msgstr ""

msgid "Bad Request"
msgstr ""

msgid "The request was invalid."
msgstr ""

msgid "Forbidden"
msgstr ""

msgid "The key was invalid (e.g. key not found, file found but key not in the file)."
msgstr ""

msgid "Unprocessable Entity"
msgstr ""

msgid "The URLs don't belong to the host or the key is not matching the schema in the protocol."
msgstr ""

msgid "Too Many Requests"
msgstr ""

msgid "Too Many Requests (potential Spam)."
msgstr ""

msgid "Auto-Submit Post Types"
msgstr ""

msgid "Submit posts from these post types automatically to the IndexNow API when a post is published, updated, or trashed."
msgstr ""

msgid "API Key"
msgstr ""

msgid "The IndexNow API key proves the ownership of the site. It is generated automatically. You can change the key if it becomes known to third parties."
msgstr ""

msgid "Change Key"
msgstr ""

msgid "API Key Location"
msgstr ""

msgid "Check Key"
msgstr ""

#. Translators: %s is the words "Check Key".
msgid "Use the %1$s button to verify that the key is accessible for search engines. Clicking on it should open the key file in your browser and show the API key."
msgstr ""

msgid "Links: "
msgstr ""

msgid "Internal Links"
msgstr ""

msgid "External Links"
msgstr ""

msgid "Incoming Links"
msgstr ""

msgid "Person or Company"
msgstr ""

msgid "Organization"
msgstr ""

msgid "Choose whether the site represents a person or an organization."
msgstr ""

msgid "Website Name"
msgstr ""

msgid "Enter the name of your site to appear in search results."
msgstr ""

msgid "Website Alternate Name"
msgstr ""

msgid "An alternate version of your site name (for example, an acronym or shorter name)."
msgstr ""

msgid "Person/Organization Name"
msgstr ""

msgid "Your name or company name intended to feature in Google's Knowledge Panel."
msgstr ""

msgid "Provide a detailed description of your organization."
msgstr ""

msgid "Logo"
msgstr ""

msgid "<strong>Min Size: 112Χ112px</strong>.<br /> A squared image is preferred by the search engines."
msgstr ""

msgid "URL of your website or your company’s website."
msgstr ""

msgid "Email"
msgstr ""

msgid "Enter the contact email address that could be displayed on search engines."
msgstr ""

msgid "Phone"
msgstr ""

msgid "Search engines may prominently display your contact phone number for mobile users."
msgstr ""

msgid "Address"
msgstr ""

msgid "Address Format"
msgstr ""

msgid "Format used when the address is displayed using the <code>[rank_math_contact_info]</code> shortcode.<br><strong>Available Tags: {address}, {locality}, {region}, {postalcode}, {country}, {gps}</strong>"
msgstr ""

msgid "Business Type"
msgstr ""

msgid "Opening Hours Format"
msgstr ""

msgid "Time format used in the contact shortcode."
msgstr ""

msgid "Opening Hours"
msgstr ""

msgid "Select opening hours. You can add multiple sets if you have different opening or closing hours on some days or if you have a mid-day break. Times are specified using 24:00 time."
msgstr ""

msgid "Add time"
msgstr ""

msgid "Monday"
msgstr ""

msgid "Tuesday"
msgstr ""

msgid "Wednesday"
msgstr ""

msgid "Thursday"
msgstr ""

msgid "Friday"
msgstr ""

msgid "Saturday"
msgstr ""

msgid "Sunday"
msgstr ""

msgid "e.g. 09:00-17:00"
msgstr ""

msgid "Phone Number"
msgstr ""

msgid "Add number"
msgstr ""

msgid "Format: ******-555-1212"
msgstr ""

msgid "Price Range"
msgstr ""

msgid "The price range of the business, for example $$$."
msgstr ""

msgid "Additional Info"
msgstr ""

msgid "Provide relevant details of your company to include in the Organization Schema."
msgstr ""

msgid "Add"
msgstr ""

msgid "Select Page"
msgstr ""

msgid "About Page"
msgstr ""

msgid "Select a page on your site where you want to show the LocalBusiness meta data."
msgstr ""

msgid "Contact Page"
msgstr ""

msgid "Google Maps API Key"
msgstr ""

#. translators: %s expands to "Google Maps Embed API" https://developers.google.com/maps/documentation/embed
msgid "An API Key is required to display embedded Google Maps on your site. Get it here: %s"
msgstr ""

#. translators: %s expands to "Google Maps Embed API" https://developers.google.com/maps/documentation/embed
msgid "Google Maps Embed API"
msgstr ""

msgid "Geo Coordinates"
msgstr ""

msgid "Latitude and longitude values separated by comma."
msgstr ""

#. Translators: placeholder is a link to the Pro version
msgid "Multiple Locations are available in the %s."
msgstr ""

#. translators: Link to kb article
msgid "Easily create redirects without fiddling with tedious code. %s."
msgstr ""

msgid "No valid ID found."
msgstr ""

msgid "No valid action found."
msgstr ""

msgid "Add New"
msgstr ""

msgid "Export Options"
msgstr ""

msgid "Redirection successfully activated."
msgstr ""

msgid "Redirection successfully deactivated."
msgstr ""

msgid "Redirection successfully moved to Trash."
msgstr ""

msgid "Redirection successfully restored."
msgstr ""

#. translators: delete counter
msgid "%d redirection(s) successfully deleted."
msgstr ""

#. Translators: Placeholder expands to number of redirections.
msgid "Warning: you have more than %d active redirections. Exporting them to your .htaccess file may cause performance issues."
msgstr ""

msgid "Export to .htaccess"
msgstr ""

msgid "Export to Nginx config file"
msgstr ""

msgid "Export"
msgstr ""

msgid "Redirection successfully deleted."
msgstr ""

msgid "Can't update redirection."
msgstr ""

msgid "Redirection updated successfully."
msgstr ""

msgid "New redirection created."
msgstr ""

#. translators: source pattern
msgid "Invalid regex pattern: %s"
msgstr ""

msgid "Redirection Count"
msgstr ""

msgid "Total number of Redirections created in the Rank Math."
msgstr ""

msgid "Redirection Hits"
msgstr ""

msgid "Total number of hits received by all the Redirections."
msgstr ""

msgid "Create and edit redirections"
msgstr ""

msgid "Manage Redirections"
msgstr ""

msgid "Redirection Settings"
msgstr ""

msgid "&raquo; Redirect this page"
msgstr ""

msgid "Redirect the current URL"
msgstr ""

msgid "No redirections found in Trash."
msgstr ""

msgid "No redirections added yet. <a href=\"#\" class=\"rank-math-add-new-redirection\">Add New Redirection</a>"
msgstr ""

msgid "Show more"
msgstr ""

msgid "Hide details"
msgstr ""

msgid "Hide"
msgstr ""

msgid "Restore"
msgstr ""

msgid "Delete Permanently"
msgstr ""

msgid "Edit"
msgstr ""

msgid "Deactivate"
msgstr ""

msgid "Activate"
msgstr ""

msgid "Trash"
msgstr ""

msgid "From"
msgstr ""

msgid "To"
msgstr ""

msgid "Type"
msgstr ""

msgid "Created"
msgstr ""

msgid "Last Accessed"
msgstr ""

msgid "Move to Trash"
msgstr ""

msgid "Active"
msgstr ""

msgid "Inactive"
msgstr ""

msgid "Empty Trash"
msgstr ""

#. translators: %1$s: post type label, %2$s: edit redirection URL.
#. translators: %1$s: term name, %2$s: edit redirection URL.
msgid "SEO Notice: you just changed the slug of a %1$s and Rank Math has automatically created a redirection. You can edit the redirection by <a href=\"%2$s\">clicking here</a>."
msgstr ""

#. translators: 1. url to new screen, 2. old trashed post permalink
msgid "<strong>SEO Notice:</strong> A previously published %1$s has been moved to trash. You may redirect <code>%2$s</code> to <a href=\"%3$s\">a new url</a>."
msgstr ""

msgid "Rank Math SEO Redirection Debugger"
msgstr ""

msgid "Redirection Debugger"
msgstr ""

msgid "Redirecting from "
msgstr ""

msgid " To "
msgstr ""

#. translators: countdown seconds
msgid "Redirecting in %s seconds..."
msgstr ""

msgid "Stop Redirection"
msgstr ""

msgid "Continue redirecting"
msgstr ""

msgid "Manage This Redirection"
msgstr ""

msgid "or"
msgstr ""

msgid "Manage All Redirections"
msgstr ""

msgid "<strong>Note:</strong> This interstitial page is displayed only to administrators. Site visitors are redirected without delay."
msgstr ""

msgid "Served from cache"
msgstr ""

msgid "<strong>Edit</strong> redirection details: from/to URLs and the redirection type."
msgstr ""

msgid "<strong>Activate/Deactivate</strong> redirections. Deactivated redirections do not take effect on your site."
msgstr ""

msgid "<strong>Delete</strong> permanently removes the redirection."
msgstr ""

msgid "You can also activate, deactivate, or delete multiple items at once using the Bulk Actions dropdown."
msgstr ""

msgid "Here you can set up custom redirections. It is important to choose the right type of redirection."
msgstr ""

msgid "301 redirections are <em>permanent</em>. The old URL will be removed in search engines and replaced by the new one, passing on SearchRank and other SEO scores. Browsers may also store the new URL in cache and redirect to it even after the redirection is deleted from the list here."
msgstr ""

msgid "Using a 302 <em>temporary</em> redirection is useful when you want to test a new page for client feedback temporarily without affecting the SEO scores of the original page."
msgstr ""

msgid "Redirections can be exported to your .htaccess file for faster redirections, in SEO > Settings > Import/Export."
msgstr ""

msgid "Debug Redirections"
msgstr ""

msgid "Display the Debug Console instead of being redirected. Administrators only."
msgstr ""

msgid "Fallback Behavior"
msgstr ""

msgid "If nothing similar is found, this behavior will be applied. <strong>Note</strong>: If the requested URL ends with <code>/login</code>, <code>/admin</code>, or <code>/dashboard</code>, WordPress will automatically redirect to respective locations within the WordPress admin area."
msgstr ""

msgid "Default 404"
msgstr ""

msgid "Redirect to Homepage"
msgstr ""

msgid "Custom Redirection"
msgstr ""

msgid "Custom Url "
msgstr ""

msgid "Redirection Type"
msgstr ""

msgid "Auto Post Redirect"
msgstr ""

msgid "Extend the functionality of WordPress by creating redirects in our plugin when you change the slug of a post, page, category or a CPT. You can modify the redirection further according to your needs."
msgstr ""

msgid "Edit robots.txt"
msgstr ""

#. translators: Link to kb article
msgid "Edit your robots.txt file to control what bots see. %s."
msgstr ""

msgid "Leave the field empty to let WordPress handle the contents dynamically. If an actual robots.txt file is present in the root folder of your site, this option won't take effect and you have to edit the file directly, or delete it and then edit from here."
msgstr ""

msgid "Contents are locked because a robots.txt file is present in the root folder."
msgstr ""

msgid "Rank Math could not detect if a robots.txt file exists or not because of a filesystem issue. The file contents entered here may not be applied."
msgstr ""

msgid "robots.txt file is not writable."
msgstr ""

#. Translators: placeholder is the Settings page URL.
msgid "<strong>Warning:</strong> your site's search engine visibility is set to Hidden in <a href=\"%1$s\" target=\"_blank\">Settings &gt; Reading</a>. This means that the changes you make here will not take effect. Set the search engine visibility to Public to be able to change the robots.txt content."
msgstr ""

#. Translators: placeholder is the URL to the robots.txt tester tool.
msgid "Test and edit your live robots.txt file with our <a href=\"%1$s\" target=\"_blank\">Robots.txt Tester</a>."
msgstr ""

msgid "Titles & Meta Settings"
msgstr ""

msgid "Sitemap Settings"
msgstr ""

msgid "404 Monitor Log"
msgstr ""

msgid "Link Builder"
msgstr ""

msgid "Site-Wide Analysis"
msgstr ""

msgid "On-Page Analysis"
msgstr ""

msgid "On-Page General Settings"
msgstr ""

msgid "On-Page Advanced Settings"
msgstr ""

msgid "On-Page Schema Settings"
msgstr ""

msgid "On-Page Social Settings"
msgstr ""

msgid "Top Admin Bar"
msgstr ""

msgid "Blocks"
msgstr ""

msgid "Take control over the default settings available for Rank Math Blocks."
msgstr ""

#. translators: %d is the number of days.
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""
msgstr[1] ""

#. translators: %d is the number of hours.
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""
msgstr[1] ""

#. translators: %d is the number of minutes.
#. translators: %d: minutes
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholders are units of time, e.g. '1 hour and 30 minutes'
msgid "%1$s and %2$s"
msgstr ""

#. translators: placeholders are units of time, e.g. '1 day, 8 hours and 30 minutes'
msgid "%1$s, %2$s and %3$s"
msgstr ""

msgid "Total Time:"
msgstr ""

msgid "Table of Contents Title"
msgstr ""

msgid "Enter the default title to use for the Table of Contents block."
msgstr ""

msgid "Table of Contents"
msgstr ""

msgid "Table of Contents List style"
msgstr ""

msgid "Select the default list style for the Table of Contents block."
msgstr ""

msgid "Numbered"
msgstr ""

msgid "Unordered"
msgstr ""

msgid "Table of Contents Exclude Headings"
msgstr ""

msgid "Choose the headings to exclude from the Table of Contents block."
msgstr ""

msgid "Heading H1"
msgstr ""

msgid "Heading H2"
msgstr ""

msgid "Heading H3"
msgstr ""

msgid "Heading H4"
msgstr ""

msgid "Heading H5"
msgstr ""

msgid "Heading H6"
msgstr ""

msgid "Schema"
msgstr ""

msgid "No schema found."
msgstr ""

msgid "Days"
msgstr ""

msgid "Opening Time"
msgstr ""

msgid "Closing Time"
msgstr ""

msgid "Editor's Rating:"
msgstr ""

msgid "Author"
msgstr ""

msgid "Edition"
msgstr ""

msgid "Name"
msgstr ""

msgid "Url"
msgstr ""

msgid "ISBN"
msgstr ""

msgid "Date Published"
msgstr ""

msgid "Format"
msgstr ""

msgid "Course Provider"
msgstr ""

msgid "Course Provider Name"
msgstr ""

msgid "Course Provider URL"
msgstr ""

msgid "Course Mode"
msgstr ""

msgid "Course Workload"
msgstr ""

msgid "Start Date"
msgstr ""

msgid "End Date"
msgstr ""

msgid "Duration"
msgstr ""

msgid "Repeat Count"
msgstr ""

msgid "Repeat Frequency"
msgstr ""

msgid "Course Type"
msgstr ""

msgid "Course Currency"
msgstr ""

msgid "Course Price"
msgstr ""

msgid "Online + Offline"
msgstr ""

msgid "Event Type"
msgstr ""

msgid "Event Attendance Mode"
msgstr ""

msgid "Event Status"
msgstr ""

msgid "Venue Name"
msgstr ""

msgid "Venue URL"
msgstr ""

msgid "Online Event URL"
msgstr ""

msgid "Performer"
msgstr ""

msgid "Performer Name"
msgstr ""

msgid "Performer URL"
msgstr ""

msgid "Ticket URL"
msgstr ""

msgid "Entry Price"
msgstr ""

msgid "Currency"
msgstr ""

msgid "Availability"
msgstr ""

msgid "Availability Starts"
msgstr ""

msgid "Stock Inventory"
msgstr ""

msgid "Salary"
msgstr ""

msgid "Salary Currency"
msgstr ""

msgid "Payroll"
msgstr ""

msgid "Date Posted"
msgstr ""

msgid "Expiry Posted"
msgstr ""

msgid "Unpublish when expired"
msgstr ""

msgid "Employment Type "
msgstr ""

msgid "Hiring Organization "
msgstr ""

msgid "Organization URL"
msgstr ""

msgid "Organization Logo"
msgstr ""

msgid "Location"
msgstr ""

msgid "Gender"
msgstr ""

msgid "Job Title"
msgstr ""

msgid "Product SKU"
msgstr ""

msgid "Product Brand"
msgstr ""

msgid "Product Currency"
msgstr ""

msgid "Product Price"
msgstr ""

msgid "Price Valid Until"
msgstr ""

msgid "Product In-Stock"
msgstr ""

msgid "Cuisine"
msgstr ""

msgid "Keywords"
msgstr ""

msgid "Recipe Yield"
msgstr ""

msgid "Calories"
msgstr ""

msgid "Preparation Time"
msgstr ""

msgid "Cooking Time"
msgstr ""

msgid "Total Time"
msgstr ""

msgid "Recipe Video Name"
msgstr ""

msgid "Recipe Video Description"
msgstr ""

msgid "Recipe Video Thumbnail"
msgstr ""

msgid "Recipe Ingredients"
msgstr ""

msgid "Recipe Instructions"
msgstr ""

msgid "Serves Cuisine"
msgstr ""

msgid "Menu URL"
msgstr ""

msgid "Service Type"
msgstr ""

msgid "Price"
msgstr ""

msgid "Price Currency"
msgstr ""

msgid "Operating System"
msgstr ""

msgid "Application Category"
msgstr ""

#. Translators: placeholder is the new Rank Math label.
msgid "SEO Analyzer %s"
msgstr ""

msgid "Site-wide analysis"
msgstr ""

msgid "Analyze this Page"
msgstr ""

msgid "SEO Analysis for this page"
msgstr ""

#. translators: API error
msgid "<strong>API Error:</strong> %s"
msgstr ""

msgid "Rank Math auto-update option is enabled on your site."
msgstr ""

#. Translators: placeholder is a HTTP error code.
msgid "HTTP %d error."
msgstr ""

msgid "Unexpected API response."
msgstr ""

msgid "(No Title)"
msgstr ""

msgid "(No Description)"
msgstr ""

msgid "Site Tagline"
msgstr ""

#. translators: link to general setting screen
msgid "Your theme may display the Site Tagline, and it can also be used in SEO titles &amp; descriptions. Set it to something unique. You can change it by navigating to <a href=\"%s\">Settings &gt; General</a>."
msgstr ""

msgid "Most WordPress themes place your site's tagline in a prominent position (inside header tags near the top of the page).  Using the right tagline can give your site an SEO boost."
msgstr ""

msgid "Unfortunately, the standard WordPress tagline is \"Just Another WordPress site.\"  That's pretty sloppy looking, and it does nothing for your SEO.  In fact, it's actually a security risk - it makes it easy for hackers with a WordPress exploit to locate your site with an automated search."
msgstr ""

#. translators: link to general setting screen
msgid "Changing your tagline is very easy.  Just head on over to <a target=\"_blank\" href=\"%1$s\">Settings - General</a> in WordPress's admin menu (on the left), or click on the link in this sentence."
msgstr ""

msgid "The tagline is the second option.  Choose a tagline that summarizes your site in a few words.  The tagline is also a good place to use your main keyword."
msgstr ""

msgid "Confirm custom tagline is set for your site"
msgstr ""

msgid "Blog Public"
msgstr ""

#. translators: link to general setting screen
msgid "Your site may not be visible to search engine."
msgstr ""

#. translators: %1$s link to the reading settings, %2$s closing tag for the link
msgid "You must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr ""

msgid "Check your site's visibility to search engines"
msgstr ""

msgid "Permalink Structure"
msgstr ""

#. translators: link to permalink setting screen
msgid "For the best SEO results, use a custom permalink structure, preferably one that includes the post title (<code>%%postname%%</code>). You can change it by navigating to <a href=\"%s\">Settings &gt; Permalinks</a>"
msgstr ""

msgid "The standard permalink structure is pretty ugly - WordPress generates offputting URLs like: http://www.yoursite.com/?p=99"
msgstr ""

msgid "It's not very kind on the eyes, and it does nothing for your site's SEO.  In fact, it can hurt it - Google's bot is quite cautious about crawling pages that look auto-generated."
msgstr ""

#. translators: link to permalink setting screen
msgid "Fortunately, it's very easy to fix.  Just hop on over to <a target=\"_blank\" href=\"%1$s\">Settings - Permalinks</a>.  Then chose the \"Post Name\" option."
msgstr ""

msgid "This option will replace the \"?p=99\" part of the URL with the post's title, like this: http://www.yoursite.com/my-amazing-post-title/"
msgstr ""

msgid "This looks nice for readers - and it gets your keywords into the URL (keywords in the URL is a ranking factor)."
msgstr ""

msgid "Check your site for SEO-friendly permalink structure"
msgstr ""

msgid "Focus Keywords"
msgstr ""

msgid "Setting focus keywords for your posts allows Rank Math to analyse the content."
msgstr ""

msgid "Rank Math allows you to set a focus keyword for every post and page you write - the option is in the \"Meta Box\", which appears under the text editor in the screen where you write and edit content."
msgstr ""

msgid "Rank Math uses these focus keywords to analyze your on-page content.  It can tell if you've done a good job of optimizing your text to rank for these keywords."
msgstr ""

msgid "Of course, if you don't give Rank Math a focus keyword to work with, it can't give you any useful feedback."
msgstr ""

msgid "Fixing this issue is easy - just edit the post, and set a Focus Keyword.  Then follow Rank Math's analysis to improve your rankings."
msgstr ""

msgid "Confirm focus keywords are set for all your posts"
msgstr ""

msgid "Post Titles Missing Focus Keywords"
msgstr ""

msgid "Make sure the focus keywords you set for the posts appear in their titles."
msgstr ""

msgid "HTML Page Titles play a large role in Google's ranking algorithm.  When you add a Focus Keyword to a post or page, Rank Math will check to see that you used the keyword in the title.  If it finds any posts or pages that are missing the keyword in the title, it will tell you here."
msgstr ""

msgid "Fixing the issue is simple - just edit the post/page and add the focus keyword(s) to the title."
msgstr ""

msgid "Verify the presence of focus keywords in your post titles"
msgstr ""

#. translators: link to plugin setting screen
msgid "Register at Google Search Console and verificate your site by adding the code to <a href=\"%1$s\">Settings &gt; Verificate Tools</a>, then navigate to <a href=\"%2$s\">Settings &gt; Search Console</a> to authenticate and link your site."
msgstr ""

msgid "Google's Search Console is a vital source of information concerning your rankings and click-through rates.  Rank Math can import this data, so you don't have to log into your Google account to get the data you need."
msgstr ""

#. translators: link to plugin search console setting screen
msgid "You can integrate the Google Search Console with Rank math in the <a href=\"%1$s\" target=\"_blank\">Search Console tab</a>. of Rank Math's General Settings menu."
msgstr ""

#. translators: Link to Search Console KB article
msgid "Read <a href=\"%1$s\" target=\"_blank\">this article</a> for detailed instructions on setting up your Google Webmaster account and getting Rank Math to work with the Google Search Console."
msgstr ""

msgid "Confirm if Rank Math is connected to Search Console"
msgstr ""

msgid "Sitemaps"
msgstr ""

msgid "XML sitemaps are a special type of text file that tells search engines about the structure of your site. They're a list of all the resources (pages and files) you would like the search engine to index. You can assign different priorities, so certain pages will be crawled first. Before XML sitemaps, search engines were limited to indexing the content they could find by following links. That's still an important feature for search engine spiders, but XML sitemaps have made it easier for content creators and search engines to collaborate."
msgstr ""

msgid "If you don't have an XML sitemap, the best option is to install a plugin that creates sitemaps for you. That way you'll know the sitemap will always be up-to-date. Plugins can also automatically ping the search engines when the XML file is updated. The Rank Math WordPress plugin gives you complete control over your site's XML sitemaps. You can control the settings for each page as you write or edit it, and Rank Math will ping Google as soon as you submit your edits. This results in fast crawls and indexing."
msgstr ""

msgid "Check the presence of sitemaps on your website"
msgstr ""

msgid "Automatic Updates"
msgstr ""

msgid "Enable automatic updates to ensure you are always using the latest version of Rank Math."
msgstr ""

msgid "Verify auto-updates are enabled for Rank Math"
msgstr ""

msgid "Site wide plugins auto-update option is disabled on your site."
msgstr ""

#. Translators: placeholder is an activate button.
msgid "Automatic updates are not enabled on your site. %s"
msgstr ""

msgid "Enable Auto Updates"
msgstr ""

#. translators: %1$s link to the customize settings, %2$s closing tag for the link
msgid "You have not entered a tagline yet. It is a good idea to choose one. %1$sYou can fix this in the customizer%2$s."
msgstr ""

msgid "Your Site Tagline is set to the default value <em>Just another WordPress site</em>."
msgstr ""

msgid "Your Site Tagline is set to a custom value."
msgstr ""

msgid "Permalinks are set to the default value. <em>Pretty permalinks</em> are disabled. "
msgstr ""

msgid "Permalinks are set to a custom structure but the post titles do not appear in the permalinks."
msgstr ""

#. translators: permalink structure
msgid "Post permalink structure is set to %s."
msgstr ""

msgid "Google Search Console has been linked."
msgstr ""

msgid "You have not linked Google Search Console yet."
msgstr ""

msgid "Could not check Focus Keywords in posts - the post meta table exceeds the size limit."
msgstr ""

msgid "All published posts have focus keywords set."
msgstr ""

#. translators: post type links
msgid "There are %s with no focus keyword set."
msgstr ""

msgid "Focus keywords appear in the titles of published posts where it is set."
msgstr ""

#. translators: post ID count
msgid "+%d More..."
msgstr ""

#. translators: post type links
msgid "There are %s published posts where the primary focus keyword does not appear in the post title."
msgstr ""

msgid "Your site has one or more sitemaps."
msgstr ""

msgid "No sitemaps found."
msgstr ""

msgid "Attention: Search Engines can't see your website."
msgstr ""

#. translators: %1$s: opening tag of the link, %2$s: the closing tag
msgid "Navigate to %1$sSettings > Reading%2$s and turn off this option: \"Discourage search engines from indexing this site\"."
msgstr ""

msgid "Your site is accessible by search engine."
msgstr ""

msgid "General"
msgstr ""

msgid "This tab contains General settings related to the XML sitemaps."
msgstr ""

#. translators: sitemap url
msgid "Your sitemap index can be found here: %s"
msgstr ""

msgid "This tab contains settings related to the HTML sitemap."
msgstr ""

#. translators: Learn more link.
msgid "Set the sitemap options for author archive pages. %s."
msgstr ""

msgid "attachments"
msgstr ""

msgid "your product pages"
msgstr ""

#. translators: Post Type label
msgid "single %s"
msgstr ""

#. translators: %1$s: thing, %2$s: Learn more link.
msgid "Change Sitemap settings of %1$s. %2$s."
msgstr ""

#. translators: Post Type Sitemap Url
#. translators: Taxonomy Sitemap Url
msgid "Sitemap URL: %s"
msgstr ""

msgid "Please note that this will add the attachment page URLs to the sitemap, not direct image URLs."
msgstr ""

msgid "Taxonomies:"
msgstr ""

#. translators: Taxonomy singular label
msgid "your product %s pages"
msgstr ""

#. translators: Taxonomy singular label
msgid "%s archives"
msgstr ""

msgid "Exclude this attachment from sitemap"
msgstr ""

#. Translators: the placeholder is for the sitemap base url.
msgid "Since you are using an NGINX server, you may need to add the following code to your %s <strong>if your Sitemap pages are not loading</strong>. If you are unsure how to do it, please contact your hosting provider."
msgstr ""

msgid "configuration file"
msgstr ""

msgid "Click here to see the code."
msgstr ""

msgid "I already added"
msgstr ""

#. Translators: placeholder is the post type name.
msgid "Rank Math has detected a new post type: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a> and <a href=\"%3$s\">the Sitemap</a>."
msgstr ""

#. Translators: placeholder is the post type names separated with commas.
msgid "Rank Math has detected new post types: %1$s. You may want to check the settings of the <a href=\"%2$s\">Titles &amp; Meta page</a> and <a href=\"%3$s\">the Sitemap</a>."
msgstr ""

#. translators: 1. separator, 2. blogname
msgid "XML Sitemap %1$s %2$s"
msgstr ""

#. translators: 1. separator, 2. blogname
msgid "Locations Sitemap %1$s %2$s"
msgstr ""

msgid "Include in Sitemap"
msgstr ""

msgid "Include author archives in the XML sitemap."
msgstr ""

msgid "Include in HTML Sitemap"
msgstr ""

msgid "Include author archives in the HTML sitemap if it's enabled."
msgstr ""

msgid "Include Authors Without Posts"
msgstr ""

msgid "Enable this option to include authors in the sitemap even if they have not created any posts. This ensures all author archives are listed, regardless of content availability."
msgstr ""

msgid "Exclude User Roles"
msgstr ""

msgid "Selected roles will be excluded from the XML &amp; HTML sitemaps."
msgstr ""

msgid "Exclude Users"
msgstr ""

msgid "Add user IDs, separated by commas, to exclude them from the sitemap."
msgstr ""

#. translators: hreflang tags documentation link
msgid "Rank Math generates the default Sitemaps only and WPML takes care of the rest. When a search engine bot visits any post/page, it is shown hreflang tag that helps it crawl the translated pages. This is one of the recommended methods by Google. Please %s"
msgstr ""

msgid "read here"
msgstr ""

msgid "Links Per Sitemap"
msgstr ""

msgid "Max number of links on each sitemap page."
msgstr ""

msgid "Images in Sitemaps"
msgstr ""

msgid "Include reference to images from the post content in sitemaps. This helps search engines index the important images on your pages."
msgstr ""

msgid "Include Featured Images"
msgstr ""

msgid "Include the Featured Image too, even if it does not appear directly in the post content."
msgstr ""

msgid "Exclude Posts"
msgstr ""

msgid "Enter post IDs of posts you want to exclude from the sitemap, separated by commas. This option **applies** to all posts types including posts, pages, and custom post types."
msgstr ""

msgid "Exclude Terms"
msgstr ""

msgid "Add term IDs, separated by comma. This option is applied for all taxonomies."
msgstr ""

msgid "Enable the HTML sitemap."
msgstr ""

msgid "Display Format"
msgstr ""

msgid "Choose how you want to display the HTML sitemap."
msgstr ""

msgid "Shortcode"
msgstr ""

msgid "Page"
msgstr ""

msgid "Use this shortcode to display the HTML sitemap."
msgstr ""

msgid "Selected page: "
msgstr ""

#. translators: link to the selected page
msgid "Selected page: <a href=\"%s\" target=\"_blank\" class=\"rank-math-selected-page\">%s</a>"
msgstr ""

msgid "Select the page to display the HTML sitemap. Once the settings are saved, the sitemap will be displayed below the content of the selected page."
msgstr ""

msgid "Select a page"
msgstr ""

msgid "Sort By"
msgstr ""

msgid "Choose how you want to sort the items in the HTML sitemap."
msgstr ""

msgid "Published Date"
msgstr ""

msgid "Modified Date"
msgstr ""

msgid "Alphabetical"
msgstr ""

msgid "Post ID"
msgstr ""

msgid "Show Dates"
msgstr ""

msgid "Show published dates for each post & page."
msgstr ""

msgid "Item Titles"
msgstr ""

msgid "Show the post/term titles, or the SEO titles in the HTML sitemap."
msgstr ""

msgid "SEO Titles"
msgstr ""

#. translators: The settings page link
msgid "To configure meta tags for your media attachment pages, you need to first %s to parent."
msgstr ""

#. translators: The settings page link
msgid "disable redirect attachments"
msgstr ""

msgid "Include this post type in the XML sitemap."
msgstr ""

msgid "Include this post type in the HTML sitemap if it's enabled."
msgstr ""

msgid "Image Custom Fields"
msgstr ""

msgid "Insert custom field (post meta) names which contain image URLs to include them in the sitemaps. Add one per line."
msgstr ""

msgid "Include archive pages for terms of this taxonomy in the XML sitemap."
msgstr ""

msgid "Include archive pages for terms of this taxonomy in the HTML sitemap."
msgstr ""

msgid "Include Empty Terms"
msgstr ""

msgid "Include archive pages of terms that have no posts associated."
msgstr ""

msgid "KML File"
msgstr ""

#. translators: link to rankmath.com
msgid "This KML File is generated by <a href=\"%s\" target=\"_blank\">Rank Math WordPress SEO Plugin</a>. It is used to provide location information to Google."
msgstr ""

#. translators: link to rankmath.com
msgid "Learn more about <a href=\"%s\" target=\"_blank\">KML File</a>."
msgstr ""

#. translators: xsl value count
msgid "<a href=\"%s\">&#8592; Sitemap Index</a>"
msgstr ""

msgid "Phone number"
msgstr ""

msgid "Latitude"
msgstr ""

msgid "Longitude"
msgstr ""

msgid "XML Sitemap"
msgstr ""

#. translators: link to rankmath.com
msgid "This XML Sitemap is generated by <a href=\"%s\" target=\"_blank\">Rank Math WordPress SEO Plugin</a>. It is what search engines like Google use to crawl and re-crawl posts/pages/products/images/archives on your website."
msgstr ""

#. translators: link to rankmath.com
msgid "Learn more about <a href=\"%s\" target=\"_blank\">XML Sitemaps</a>."
msgstr ""

#. translators: xsl value count
msgid "This XML Sitemap Index file contains <strong>%s</strong> sitemaps."
msgstr ""

msgid "Last Modified"
msgstr ""

#. translators: xsl value count
msgid "This XML Sitemap contains <strong>%s</strong> URLs."
msgstr ""

msgid "Last Mod."
msgstr ""

msgid "Unable to create backup this time."
msgstr ""

msgid "Backup created successfully."
msgstr ""

msgid "Backup does not exist."
msgstr ""

msgid "Backup restored successfully."
msgstr ""

msgid "No backup key found to delete."
msgstr ""

msgid "Backup successfully deleted."
msgstr ""

msgid "The error log cannot be retrieved."
msgstr ""

msgid "The error log cannot be retrieved: Error log file is too large."
msgstr ""

msgid "Settings could not be imported:"
msgstr ""

msgid "Settings could not be imported: Upload failed."
msgstr ""

msgid "Uploaded file could not be read."
msgstr ""

msgid "Settings successfully imported. Your old configuration has been saved as a backup."
msgstr ""

msgid "No settings found to be imported."
msgstr ""

msgid "Sorry, you are not authorized to Import/Export the settings."
msgstr ""

msgid "Invalid action selected."
msgstr ""

msgid "Status & Tools"
msgstr ""

msgid "Version"
msgstr ""

msgid "Database version"
msgstr ""

msgid "Plugin subscription plan"
msgstr ""

msgid "Active modules"
msgstr ""

msgid "(none)"
msgstr ""

msgid "Google Refresh token"
msgstr ""

msgid "No token"
msgstr ""

msgid "Token exists"
msgstr ""

msgid "Google Permission"
msgstr ""

msgid "Database Table: 404 Log"
msgstr ""

msgid "Database Table: Redirection"
msgstr ""

msgid "Database Table: Redirection Cache"
msgstr ""

msgid "Database Table: Internal Link"
msgstr ""

msgid "Database Table: Internal Link Meta"
msgstr ""

msgid "Database Table: Google Search Console"
msgstr ""

msgid "Database Table: Flat Posts"
msgstr ""

msgid "Database Table: Google Analytics"
msgstr ""

msgid "Database Table: Google AdSense"
msgstr ""

msgid "Database Table: Keyword Manager"
msgstr ""

msgid "Database Table: Inspections"
msgstr ""

msgid "Not found"
msgstr ""

msgid "This update will install a beta version of Rank Math."
msgstr ""

msgid "Rollback Plugin"
msgstr ""

msgid "Plugin rollback successful."
msgstr ""

msgid "Installing the rollback version&#8230;"
msgstr ""

#. translators: Link to kb article
msgid "Choose how you want Rank Math to handle your WooCommerce SEO. %s."
msgstr ""

msgid "Product's price."
msgstr ""

msgid "Product's price of the current product"
msgstr ""

msgid "Product's SKU."
msgstr ""

msgid "Product's SKU of the current product"
msgstr ""

msgid "Product's short description."
msgstr ""

msgid "Product's short description of the current product"
msgstr ""

msgid "Product's brand."
msgstr ""

msgid "Product's brand of the current product"
msgstr ""

msgid "Remove base"
msgstr ""

#. translators: 1. Example text 2. Example text
msgid "Remove prefix like %1$s from product URL chosen at %2$s"
msgstr ""

msgid "WordPress Dashboard > Settings > Permalinks > Product permalinks Example: default: /product/accessories/action-figures/acme/ - becomes: /accessories/action-figures/acme/"
msgstr ""

msgid "Remove category base"
msgstr ""

msgid "Remove prefix from category URL."
msgstr ""

msgid "default: /product-category/accessories/action-figures/ - changed: /accessories/action-figures/"
msgstr ""

msgid " Remove parent slugs"
msgstr ""

msgid "Remove parent slugs from category URL."
msgstr ""

msgid "default: /product-category/accessories/action-figures/ - changed: /product-category/action-figures/"
msgstr ""

msgid "Remove Generator Tag"
msgstr ""

msgid "Remove WooCommerce generator tag from the source code."
msgstr ""

msgid "Remove Schema Markup on Shop Archives"
msgstr ""

msgid "Remove Schema Markup Data from WooCommerce Shop archive pages."
msgstr ""

msgid "Brand"
msgstr ""

msgid "Select Product Brand Taxonomy to use in Schema.org & OpenGraph markup."
msgstr ""

msgid "In stock"
msgstr ""

msgid "Written by"
msgstr ""

msgid "Time to read"
msgstr ""

msgid "Less than a minute"
msgstr ""

msgid "Items"
msgstr ""

msgid "Posts"
msgstr ""

msgid "ID of the current post/page"
msgstr ""

msgid "Focus Keyword of the current post"
msgstr ""

msgid "Focus Keywords of the current post"
msgstr ""

msgid "Custom Field (advanced)"
msgstr ""

msgid "Custom field value."
msgstr ""

msgid "Custom field value"
msgstr ""

msgid "Page number with context (i.e. page 2 of 4). Only displayed on page 2 and above."
msgstr ""

msgid "Page Number"
msgstr ""

msgid "Current page number"
msgstr ""

msgid "Max Pages"
msgstr ""

msgid "Max pages number"
msgstr ""

msgid "Post Type Name Singular"
msgstr ""

msgid "Name of current post type (singular)"
msgstr ""

msgid "Post Type Name Plural"
msgstr ""

msgid "Name of current post type (plural)"
msgstr ""

msgid "Products"
msgstr ""

#. translators: %1$d: current page number, %2$d: max pages.
msgid "Page %1$d of %2$d"
msgstr ""

msgid "Author ID"
msgstr ""

msgid "Author's user ID of the current post, page or author archive."
msgstr ""

msgid "Post Author"
msgstr ""

msgid "Display author's nicename of the current post, page or author archive."
msgstr ""

msgid "Author Description"
msgstr ""

msgid "Author's biographical info of the current post, page or author archive."
msgstr ""

msgid "Example Post title"
msgstr ""

msgid "Variable names can only contain alphanumeric characters, underscores and dashes."
msgstr ""

msgid "The variable has already been registered."
msgstr ""

msgid "Separator Character"
msgstr ""

msgid "Separator character, as set in the Title Settings"
msgstr ""

msgid "Search Query"
msgstr ""

msgid "Search query (only available on search results page)"
msgstr ""

msgid "example search"
msgstr ""

msgid "Counter"
msgstr ""

msgid "Starts at 1 and increments by 1."
msgstr ""

msgid "File Name"
msgstr ""

msgid "File Name of the attachment"
msgstr ""

msgid "Site Title"
msgstr ""

msgid "Title of the site"
msgstr ""

msgid "Site Description"
msgstr ""

msgid "Description of the site"
msgstr ""

msgid "Current Date"
msgstr ""

msgid "Current server date"
msgstr ""

msgid "Current Day"
msgstr ""

msgid "Current server day"
msgstr ""

msgid "Current Month"
msgstr ""

msgid "Current server month"
msgstr ""

msgid "Current Year"
msgstr ""

msgid "Current server year"
msgstr ""

msgid "Current Time"
msgstr ""

msgid "Current server time"
msgstr ""

msgid "Current Time (advanced)"
msgstr ""

msgid "Current server time with custom formatting pattern."
msgstr ""

msgid "Organization Name"
msgstr ""

msgid "The Organization Name added in Local SEO Settings."
msgstr ""

msgid "Organization Logo added in Local SEO Settings."
msgstr ""

msgid "Organization URL added in Local SEO Settings."
msgstr ""

#. translators: Taxonomy name.
msgid "%s Title"
msgstr ""

#. translators: Taxonomy name.
msgid "%s Description"
msgstr ""

msgid "Custom Term title."
msgstr ""

msgid "Custom Term description."
msgstr ""

msgid "Post Title"
msgstr ""

msgid "Title of the current post/page"
msgstr ""

msgid "Post Title of parent page"
msgstr ""

msgid "Title of the parent page of the current post/page"
msgstr ""

msgid "Post Excerpt"
msgstr ""

msgid "Excerpt of the current post (or auto-generated if it does not exist)"
msgstr ""

msgid "Excerpt of the current post (without auto-generation)"
msgstr ""

msgid "Post Excerpt Only"
msgstr ""

msgid "Custom or Generated SEO Title of the current post/page"
msgstr ""

msgid "SEO Description"
msgstr ""

msgid "Custom or Generated SEO Description of the current post/page"
msgstr ""

msgid "Post URL"
msgstr ""

msgid "URL of the current post/page"
msgstr ""

msgid "Post Thumbnail"
msgstr ""

msgid "Current Post Thumbnail"
msgstr ""

msgid "Publication date of the current post/page <strong>OR</strong> specified date on date archives"
msgstr ""

msgid "Date Modified"
msgstr ""

msgid "Last modification date of the current post/page"
msgstr ""

msgid "Date Published (advanced)"
msgstr ""

msgid "Publish date with custom formatting pattern."
msgstr ""

msgid "Date Modified (advanced)"
msgstr ""

msgid "Modified date with custom formatting pattern."
msgstr ""

msgid "Post Category"
msgstr ""

msgid "First category (alphabetically) associated to the current post <strong>OR</strong> current category on category archives"
msgstr ""

msgid "Example Category"
msgstr ""

msgid "Post Categories"
msgstr ""

msgid "Comma-separated list of categories associated to the current post"
msgstr ""

msgid "Example Category 1, Example Category 2"
msgstr ""

msgid "Categories (advanced)"
msgstr ""

msgid "Output list of categories associated to the current post, with customization options."
msgstr ""

msgid "Primary Terms"
msgstr ""

msgid "Output list of terms from the primary taxonomy associated to the current post."
msgstr ""

msgid "Post Tag"
msgstr ""

msgid "First tag (alphabetically) associated to the current post <strong>OR</strong> current tag on tag archives"
msgstr ""

msgid "Example Tag"
msgstr ""

msgid "Post Tags"
msgstr ""

msgid "Comma-separated list of tags associated to the current post"
msgstr ""

msgid "Example Tag 1, Example Tag 2"
msgstr ""

msgid "Tags (advanced)"
msgstr ""

msgid "Output list of tags associated to the current post, with customization options."
msgstr ""

msgid "Example Tag 1 | Example Tag 2"
msgstr ""

msgid "Current Term"
msgstr ""

msgid "Current term name"
msgstr ""

msgid "Example Term"
msgstr ""

msgid "Term Description"
msgstr ""

msgid "Current term description"
msgstr ""

msgid "Example Term Description"
msgstr ""

msgid "Custom Term (advanced)"
msgstr ""

msgid "Custom term value."
msgstr ""

msgid "Custom term value"
msgstr ""

msgid "Custom Term description"
msgstr ""

msgid "The $id variable is required."
msgstr ""

msgid "Example"
msgstr ""

#. translators: variable name
msgid "The $%1$s is required for variable %2$s."
msgstr ""

msgid "The redirection you are trying to create may cause an infinite loop. Please check the source and destination URLs. The redirection has been deactivated."
msgstr ""

msgid "The redirection you are trying to update may cause an infinite loop. Please check the source and destination URLs."
msgstr ""

msgid "Please add at least one valid source URL."
msgstr ""

msgid "Module slug"
msgstr ""

msgid "Invalid module"
msgstr ""

msgid "Module state either on or off"
msgstr ""

msgid "Post scores"
msgstr ""

msgid "Action to perform"
msgstr ""

msgid "Mode to set"
msgstr ""

msgid "Site disconnected successfully."
msgstr ""

msgid "Site token"
msgstr ""

msgid "URL to get HTML tags for."
msgstr ""

msgid "Selected posts to update the data for."
msgstr ""

msgid "Sorry, you are not allowed to create/update redirection."
msgstr ""

msgid "Sorry, you are not allowed to create/update schema."
msgstr ""

msgid "Sorry, you are not allowed to edit this post type."
msgstr ""

msgid "Sorry, you are not allowed to edit this post."
msgstr ""

msgid "Invalid post ID."
msgstr ""

msgid "Sorry, you are not allowed to edit this term."
msgstr ""

msgid "Invalid term ID."
msgstr ""

msgid "Sorry, field is empty which is not allowed."
msgstr ""

msgid "Sorry, the field contains invalid characters."
msgstr ""

msgid "Current Step"
msgstr ""

msgid "Current Step Data"
msgstr ""

msgid "Object unique id"
msgstr ""

msgid "Object Type i.e. post, term, user"
msgstr ""

msgid "Whether the object has a redirect or not"
msgstr ""

msgid "Redirection ID"
msgstr ""

msgid "Redirection URL"
msgstr ""

msgid "Meta to add or update data."
msgstr ""

msgid "schemas to add or update data."
msgstr ""

msgid "Enable breadcrumbs function"
msgstr ""

msgid "Turning off breadcrumbs will hide breadcrumbs inserted in template files too."
msgstr ""

#. Translators: Code to add support for Rank Math Breadcrumbs.
msgid "This option cannot be changed since your theme has added the support for Rank Math Breadcrumbs using: %s"
msgstr ""

msgid "Separator character or string that appears between breadcrumb items."
msgstr ""

msgid "Show Homepage Link"
msgstr ""

msgid "Display homepage breadcrumb in trail."
msgstr ""

msgid "Homepage label"
msgstr ""

msgid "Label used for homepage link (first item) in breadcrumbs."
msgstr ""

msgid "Homepage Link"
msgstr ""

msgid "Link to use for homepage (first item) in breadcrumbs."
msgstr ""

msgid "Prefix Breadcrumb"
msgstr ""

msgid "Prefix for the breadcrumb path."
msgstr ""

msgid "Archive Format"
msgstr ""

msgid "Format the label used for archive pages."
msgstr ""

msgid "Search Results Format"
msgstr ""

msgid "Format the label used for search results pages."
msgstr ""

msgid "404 label"
msgstr ""

msgid "Label used for 404 error item in breadcrumbs."
msgstr ""

msgid "Hide Post Title"
msgstr ""

msgid "Hide Post title from Breadcrumb."
msgstr ""

msgid "Show Category(s)"
msgstr ""

msgid "If category is a child category, show all ancestor categories."
msgstr ""

msgid "Hide Taxonomy Name"
msgstr ""

msgid "Hide Taxonomy Name from Breadcrumb."
msgstr ""

msgid "Show Blog Page"
msgstr ""

msgid "Show Blog Page in Breadcrumb."
msgstr ""

msgid ".htaccess file not found."
msgstr ""

msgid ".htaccess file is not writable."
msgstr ""

msgid "I understand the risks and I want to edit the file"
msgstr ""

msgid "Be careful when editing the htaccess file, it is easy to make mistakes and break your site. If that happens, you can restore the file to its state <strong>before the last edit</strong> by replacing the htaccess file with the backup copy created by Rank Math in the same directory (<em>.htaccess_back_xxxxxx</em>) using an FTP client."
msgstr ""

msgid "Strip Category Base"
msgstr ""

#. translators: Link to kb article
msgid "Remove /category/ from category archive URLs. %s <br>E.g. <code>example.com/category/my-category/</code> becomes <code>example.com/my-category</code>"
msgstr ""

#. translators: Link to kb article
msgid "Why do this?"
msgstr ""

#. translators: Redirection page url
msgid "Redirection Manager"
msgstr ""

msgid "Redirections Manager"
msgstr ""

msgid "Please enable Redirections module."
msgstr ""

msgid "Redirect Attachments"
msgstr ""

#. translators: Link to kb article
msgid "Redirect all attachment page URLs to the post they appear in. For more advanced redirection control, use the built-in %s."
msgstr ""

msgid "Redirect Orphan Attachments"
msgstr ""

msgid "Redirect attachments without a parent post to this URL. Leave empty for no redirection."
msgstr ""

msgid "Nofollow External Links"
msgstr ""

msgid "Automatically add <code>rel=\"nofollow\"</code> attribute for external links appearing in your posts, pages, and other post types. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr ""

msgid "Nofollow Image File Links"
msgstr ""

msgid "Automatically add <code>rel=\"nofollow\"</code> attribute for links pointing to external image files. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr ""

msgid "Nofollow Domains"
msgstr ""

msgid "Only add <code>nofollow</code> attribute for the link if target domain is in this list. Add one per line. Leave empty to apply nofollow for <strong>ALL</strong> external domains."
msgstr ""

msgid "Nofollow Exclude Domains"
msgstr ""

msgid "The <code>nofollow</code> attribute <strong>will not be added</strong> for the link if target domain is in this list. Add one per line."
msgstr ""

msgid "Open External Links in New Tab/Window"
msgstr ""

msgid "Automatically add <code>target=\"_blank\"</code> attribute for external links appearing in your posts, pages, and other post types to make them open in a new browser tab or window. The attribute is dynamically applied when the content is displayed, and the stored content is not changed."
msgstr ""

msgid "Headless CMS Support"
msgstr ""

#. Translators: placeholder is a link to "Read more".
msgid "Enable this option to register a REST API endpoint that returns the HTML meta tags for a given URL. %s"
msgstr ""

#. Translators: placeholder is a link to "Read more".
msgid "Read more"
msgstr ""

msgid "Show SEO Score to Visitors"
msgstr ""

msgid "Proudly display the calculated SEO Score as a badge on the front end. It can be disabled for specific posts in the post editor."
msgstr ""

msgid "SEO Score Post Types"
msgstr ""

msgid "SEO Score Template"
msgstr ""

msgid "Change the styling for the front end SEO score badge."
msgstr ""

msgid "Circle"
msgstr ""

msgid "Square"
msgstr ""

msgid "SEO Score Position"
msgstr ""

#. translators: 1.SEO Score Shortcode 2. SEO Score function
msgid "Display the badges automatically, or insert the %1$s shortcode in your posts and the %2$s template tag in your theme template files."
msgstr ""

msgid "Below Content"
msgstr ""

msgid "Above Content"
msgstr ""

msgid "Above & Below Content"
msgstr ""

msgid "Custom (use shortcode)"
msgstr ""

msgid "Support Us with a Link"
msgstr ""

#. Translators: %s is the word "nofollow" code tag and second one for the filter link
msgid "If you are showing the SEO scores on the front end, this option will insert a %1$s backlink to RankMath.com to show your support. You can change the link & the text by using this %2$s."
msgstr ""

#. Translators: %s is the word "nofollow" code tag and second one for the filter link
msgid "filter"
msgstr ""

msgid "RSS Before Content"
msgstr ""

msgid "Add content before each post in your site feeds."
msgstr ""

msgid "RSS After Content"
msgstr ""

msgid "Add content after each post in your site feeds."
msgstr ""

msgid "Available variables"
msgstr ""

msgid "Variable"
msgstr ""

msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr ""

msgid "A link to the post, with the title as anchor text."
msgstr ""

msgid "A link to your site, with your site's name as anchor text."
msgstr ""

msgid "A link to your site, with your site's name and description as anchor text."
msgstr ""

msgid "Featured image of the article."
msgstr ""

msgid "Google Search Console"
msgstr ""

#. translators: Google Search Console Link
msgid "Enter your Google Search Console verification HTML code or ID. Learn how to get it: %s"
msgstr ""

#. translators: Google Search Console Link
msgid "Search Console Verification Page"
msgstr ""

msgid "Bing Webmaster Tools"
msgstr ""

#. translators: Bing webmaster link
msgid "Enter your Bing Webmaster Tools verification HTML code or ID. Get it here: %s"
msgstr ""

#. translators: Bing webmaster link
msgid "Bing Webmaster Verification Page"
msgstr ""

#. translators: Baidu webmaster link
msgid "Baidu Webmaster Tools"
msgstr ""

#. translators: Baidu webmaster link
msgid "Enter your Baidu Webmaster Tools verification HTML code or ID. Learn how to get it: %s"
msgstr ""

msgid "Yandex Verification ID"
msgstr ""

#. translators: Yandex webmaster link
msgid "Enter your Yandex verification HTML code or ID. Learn how to get it: %s"
msgstr ""

#. translators: Yandex webmaster link
msgid "Yandex.Webmaster Page"
msgstr ""

msgid "Pinterest Verification ID"
msgstr ""

#. translators: Pinterest webmaster link
msgid "Enter your Pinterest verification HTML code or ID. Learn how to get it: %s"
msgstr ""

#. translators: Pinterest webmaster link
msgid "Pinterest Account"
msgstr ""

msgid "Norton Safe Web Verification ID"
msgstr ""

#. translators: Norton webmaster link
msgid "Enter your Norton Safe Web verification HTML code or ID. Learn how to get it: %s"
msgstr ""

#. translators: Norton webmaster link
msgid "Norton Ownership Verification Page"
msgstr ""

msgid "Custom Webmaster Tags"
msgstr ""

#. translators: %s: Allowed tags
msgid "Enter your custom webmaster tags. Only %s tags are allowed."
msgstr ""

msgid "Author Archives"
msgstr ""

msgid "Enables or disables Author Archives. If disabled, the Author Archives are redirected to your homepage. To avoid duplicate content issues, noindex author archives if you keep them enabled."
msgstr ""

msgid "Disabled"
msgstr ""

msgid "Enabled"
msgstr ""

msgid "Author Base"
msgstr ""

msgid "Change the <code>/author/</code> part in author archive URLs."
msgstr ""

#. translators: post type name
msgid "Author Robots Meta"
msgstr ""

msgid "Select custom robots meta for author page, such as <code>nofollow</code>, <code>noarchive</code>, etc. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr ""

msgid "Custom values for robots meta tag on author page."
msgstr ""

msgid "Author Advanced Robots"
msgstr ""

msgid "Author Archive Title"
msgstr ""

msgid "Title tag on author archives. SEO options for specific authors can be set with the meta box available in the user profiles."
msgstr ""

msgid "Author Archive Description"
msgstr ""

msgid "Author archive meta description. SEO options for specific author archives can be set with the meta box in the user profiles."
msgstr ""

msgid "Slack Enhanced Sharing"
msgstr ""

msgid "When the option is enabled and an author archive is shared on Slack, additional information will be shown (name & total number of posts)."
msgstr ""

msgid "Add SEO Controls"
msgstr ""

msgid "Add SEO Controls for user profile pages. Access to the Meta Box can be fine tuned with code, using a special filter hook."
msgstr ""

msgid "Robots Meta"
msgstr ""

msgid "Default values for robots meta tag. These can be changed for individual posts, taxonomies, etc."
msgstr ""

msgid "Advanced Robots Meta"
msgstr ""

msgid "Noindex Empty Category and Tag Archives"
msgstr ""

msgid "Setting empty archives to <code>noindex</code> is useful for avoiding indexation of thin content pages and dilution of page rank. As soon as a post is added, the page is updated to <code>index</code>."
msgstr ""

msgid "You can use the separator character in titles by inserting <code>%separator%</code> or <code>%sep%</code> in the title fields."
msgstr ""

msgid "Rewrite Titles"
msgstr ""

msgid "Your current theme doesn't support title-tag. Enable this option to rewrite page, post, category, search and archive page titles."
msgstr ""

msgid "Capitalize Titles"
msgstr ""

msgid "Automatically capitalize the first character of each word in the titles."
msgstr ""

msgid "OpenGraph Thumbnail"
msgstr ""

msgid "When a featured image or an OpenGraph Image is not set for individual posts/pages/CPTs, this image will be used as a fallback thumbnail when your post is shared on Facebook. The recommended image size is 1200 x 630 pixels."
msgstr ""

msgid "Twitter Card Type"
msgstr ""

msgid "Card type selected when creating a new post. This will also be applied for posts without a card type selected."
msgstr ""

msgid "Summary Card with Large Image"
msgstr ""

msgid "Summary Card"
msgstr ""

#. translators: something
msgid "Static page is set as the front page (WP Dashboard > Settings > Reading). To add SEO title, description, and meta for the homepage, please click here: %s"
msgstr ""

msgid "Edit Page: "
msgstr ""

msgid "Homepage Title"
msgstr ""

msgid "Homepage title tag."
msgstr ""

msgid "Homepage Meta Description"
msgstr ""

msgid "Homepage meta description."
msgstr ""

msgid "Homepage Robots Meta"
msgstr ""

msgid "Select custom robots meta for homepage, such as <code>nofollow</code>, <code>noarchive</code>, etc. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr ""

msgid "Custom values for robots meta tag on homepage."
msgstr ""

msgid "Homepage Advanced Robots"
msgstr ""

msgid "Homepage Title for Facebook"
msgstr ""

msgid "Title of your site when shared on Facebook, Twitter and other social networks."
msgstr ""

msgid "Homepage Description for Facebook"
msgstr ""

msgid "Description of your site when shared on Facebook, Twitter and other social networks."
msgstr ""

msgid "Homepage Thumbnail for Facebook"
msgstr ""

msgid "Image displayed when your homepage is shared on Facebook and other social networks. Use images that are at least 1200 x 630 pixels for the best display on high resolution devices."
msgstr ""

msgid "URL of the item."
msgstr ""

msgid "Date Archives"
msgstr ""

#. Translators: placeholder is an example URL.
msgid "Enable or disable the date archives (e.g: %s). If this option is disabled, the date archives will be redirected to the homepage."
msgstr ""

msgid "Date Archive Title"
msgstr ""

msgid "Title tag on day/month/year based archives."
msgstr ""

msgid "Date Archive Description"
msgstr ""

msgid "Date archive description."
msgstr ""

#. translators: post type name
msgid "Date Robots Meta"
msgstr ""

msgid "Custom values for robots meta tag on date page."
msgstr ""

msgid "Date Advanced Robots"
msgstr ""

msgid "404 Title"
msgstr ""

msgid "Title tag on 404 Not Found error page."
msgstr ""

msgid "Search Results Title"
msgstr ""

msgid "Title tag on search results page."
msgstr ""

msgid "Noindex Search Results"
msgstr ""

msgid "Prevent search results pages from getting indexed by search engines. Search results could be considered to be thin content and prone to duplicate content issues."
msgstr ""

msgid "Noindex Subpages"
msgstr ""

msgid "Prevent all paginated pages from getting indexed by search engines."
msgstr ""

msgid "Noindex Paginated Single Pages"
msgstr ""

msgid "Prevent paginated pages of single pages and posts to show up in the search results. This also applies for the Blog page."
msgstr ""

msgid "Noindex Password Protected Pages"
msgstr ""

msgid "Prevent password protected pages & posts from getting indexed by search engines."
msgstr ""

#. Translators: Post type name.
msgid "When the option is enabled and a %s is shared on Slack, additional information will be shown (estimated time to read and author)."
msgstr ""

msgid "When the option is enabled and a page is shared on Slack, additional information will be shown (estimated time to read)."
msgstr ""

msgid "When the option is enabled and a product is shared on Slack, additional information will be shown (price & availability)."
msgstr ""

msgid "When the option is enabled and a product is shared on Slack, additional information will be shown (price)."
msgstr ""

#. translators: post type name
msgid "Single %s Title"
msgstr ""

#. translators: post type name
msgid "Default title tag for single %s pages. This can be changed on a per-post basis on the post editor screen."
msgstr ""

#. translators: post type name
msgid "Single %s Description"
msgstr ""

#. translators: post type name
msgid "Default description for single %s pages. This can be changed on a per-post basis on the post editor screen."
msgstr ""

#. translators: post type name
msgid "%s Archive Title"
msgstr ""

#. translators: post type name
msgid "Title for %s archive pages."
msgstr ""

#. translators: post type name
msgid "%s Archive Description"
msgstr ""

#. translators: post type name
msgid "Description for %s archive pages."
msgstr ""

msgid "Schema Type"
msgstr ""

#. translators: link to title setting screen
msgid "Default rich snippet selected when creating a new product."
msgstr ""

#. Translators: %s is "Article" inside a <code> tag.
msgid "Default rich snippet selected when creating a new post of this type. If %s is selected, it will be applied for all existing posts with no Schema selected."
msgstr ""

msgctxt "Schema type name in a field description"
msgid "Article"
msgstr ""

msgid "Headline"
msgstr ""

#. translators: Google article snippet doc link
msgid "Google does not allow Person as the Publisher for articles. Organization will be used instead. You can read more about this <a href=\"%s\" target=\"_blank\">here</a>."
msgstr ""

msgid "Article Type"
msgstr ""

msgid "Blog Post"
msgstr ""

msgid "News Article"
msgstr ""

#. translators: post type name
msgid "%s Robots Meta"
msgstr ""

#. translators: post type name
msgid "Select custom robots meta, such as <code>nofollow</code>, <code>noarchive</code>, etc. for single %s pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr ""

#. translators: post type name
msgid "Custom values for robots meta tag on %s."
msgstr ""

#. translators: post type name
msgid "%s Advanced Robots Meta"
msgstr ""

msgid "Enable Link Suggestions meta box for this post type, along with the Pillar Content feature."
msgstr ""

msgid "Link Suggestion Titles"
msgstr ""

msgid "Use the Focus Keyword as the default text for the links instead of the post titles."
msgstr ""

msgid "Titles"
msgstr ""

msgid "Primary Taxonomy"
msgstr ""

#. translators: post type name
msgid "Choose which taxonomy you want to use with the Primary Term feature. This will also be the taxonomy shown in the Breadcrumbs when a single %1$s is being viewed."
msgstr ""

msgid "Thumbnail for Facebook"
msgstr ""

msgid "Image displayed when your page is shared on Facebook and other social networks. Use images that are at least 1200 x 630 pixels for the best display on high resolution devices."
msgstr ""

msgid "Bulk Editing"
msgstr ""

msgid "Add bulk editing columns to the post listing screen."
msgstr ""

msgid "Read Only"
msgstr ""

msgid "Add SEO controls for the editor screen to customize SEO options for posts in this post type."
msgstr ""

msgid "Custom Fields"
msgstr ""

msgid "List of custom fields name to include in the Page analysis. Add one per line."
msgstr ""

msgid "Facebook Page URL"
msgstr ""

msgid "Enter your complete Facebook page URL here. eg:"
msgstr ""

msgid "Facebook Authorship"
msgstr ""

msgid "Insert personal Facebook profile URL to show Facebook Authorship when your articles are being shared on Facebook. eg:"
msgstr ""

msgid "Facebook Admin"
msgstr ""

#. translators: numeric user ID link
msgid "Enter %s. Use a comma to separate multiple IDs. Alternatively, you can enter an app ID below."
msgstr ""

msgid "Facebook App"
msgstr ""

#. translators: numeric app ID link
msgid "Enter %s. Alternatively, you can enter a user ID above."
msgstr ""

msgid "Facebook Secret"
msgstr ""

#. translators: Learn more link
msgid "Enter alphanumeric secret ID. %s."
msgstr ""

msgid "Twitter Username"
msgstr ""

msgid "Enter the Twitter username of the author to add <code>twitter:creator</code> tag to posts. eg: <code>RankMathSEO</code>"
msgstr ""

msgid "Additional Profiles"
msgstr ""

#. translators: taxonomy name
msgid "%s Archive Titles"
msgstr ""

#. translators: taxonomy name
msgid "Title tag for %s archives"
msgstr ""

#. translators: taxonomy name
msgid "%s Archive Descriptions"
msgstr ""

#. translators: taxonomy name
msgid "Description for %s archives"
msgstr ""

#. translators: taxonomy name
msgid "%s Archives Robots Meta"
msgstr ""

#. translators: taxonomy name
msgid "Select custom robots meta, such as <code>nofollow</code>, <code>noarchive</code>, etc. for %s archive pages. Otherwise the default meta will be used, as set in the Global Meta tab."
msgstr ""

#. translators: taxonomy name
msgid "Custom values for robots meta tag on %s archives."
msgstr ""

#. translators: taxonomy name
msgid "%s Archives Advanced Robots Meta"
msgstr ""

msgid "When the option is enabled and a term from this taxonomy is shared on Slack, additional information will be shown (the total number of items with this term)."
msgstr ""

msgid "Add the SEO Controls for the term editor screen to customize SEO options for individual terms in this taxonomy."
msgstr ""

msgid "Remove Snippet Data"
msgstr ""

#. translators: taxonomy name
msgid "Remove schema data from %s."
msgstr ""

msgid "Error: Nonce verification failed"
msgstr ""

#. Translators: placeholders are opening and closing tags for connect analytics setting and using ga4 property doc.
msgid "Universal Analytics (UA) is no longer supported. Please connect your Google Analytics GA4 account by navigating to %1$sGeneral Settings → Analytics%2$s. For more details, refer to this guide: %3$sHow to Use Google Analytics 4 (GA4) Property with Rank Math%4$s."
msgstr ""

#. translators: WordPress Version
msgid "You are using the outdated WordPress, please update it to version %s or higher."
msgstr ""

#. translators: PHP Version
msgid "Rank Math requires PHP version %s or above. Please update PHP to run this plugin."
msgstr ""

#. translators: 1. Bold text 2. Bold text
msgid "%1$s A filter to remove the Rank Math data from the database is present. Deactivating & Deleting this plugin will remove everything related to the Rank Math plugin. %2$s"
msgstr ""

msgid "CAUTION:"
msgstr ""

msgid "This action is IRREVERSIBLE."
msgstr ""

msgid "Getting Started"
msgstr ""

msgid "Documentation"
msgstr ""

msgid "Add a few images and/or videos to make your content appealing."
msgstr ""

msgid "Content with images and/or video feels more inviting to users. It also helps supplement your textual content."
msgstr ""

msgid "Your content contains images and/or video(s)."
msgstr ""

msgid "You are not using rich media like images or videos."
msgstr ""

msgid "Add short and concise paragraphs for better readability and UX."
msgstr ""

msgid "Short paragraphs are easier to read and more pleasing to the eye. Long paragraphs scare the visitor, and they might result to SERPs looking for better readable content."
msgstr ""

msgid "You are using short paragraphs."
msgstr ""

msgid "At least one paragraph is long. Consider using short paragraphs."
msgstr ""

msgid "Use Table of Content to break-down your text."
msgstr ""

msgid "Table of Contents help break down content into smaller, digestible chunks. It makes reading easier which in turn results in better rankings."
msgstr ""

msgid "You seem to be using a %1$s to break-down your text."
msgstr ""

msgid "You don't seem to be using a %1$s."
msgstr ""

msgid "Keyword Density is 0. Aim for around 1% Keyword Density."
msgstr ""

msgid "There is no ideal keyword density percentage, but it should not be too high. The most important thing is to keep the copy natural."
msgstr ""

msgid "Keyword Density is %1$s which is low, the Focus Keyword and combination appears %2$s times."
msgstr ""

msgid "Keyword Density is %1$s which is high, the Focus Keyword and combination appears %2$s times."
msgstr ""

msgid "Keyword Density is %1$s, the Focus Keyword and combination appears %2$s times."
msgstr ""

msgid "Use Focus Keyword at the beginning of your content."
msgstr ""

msgid "The first 10% of the content should contain the Focus Keyword preferably at the beginning."
msgstr ""

msgid "Focus Keyword appears in the first 10% of the content."
msgstr ""

msgid "Focus Keyword doesn't appear at the beginning of your content."
msgstr ""

msgid "Use Focus Keyword in the content."
msgstr ""

msgid "It is recommended to make the focus keyword appear in the post content too."
msgstr ""

msgid "Focus Keyword found in the content."
msgstr ""

msgid "Focus Keyword doesn't appear in the content."
msgstr ""

msgid "Add an image with your Focus Keyword as alt text."
msgstr ""

msgid "It is recommended to add the focus keyword in the alt attribute of one or more images."
msgstr ""

msgid "We detected a gallery in your content & assuming that you added Focus Keyword in alt in at least one of the gallery images."
msgstr ""

msgid "Focus Keyword found in image alt attribute(s)."
msgstr ""

msgid "Focus Keyword not found in image alt attribute(s)."
msgstr ""

msgid "Add Focus Keyword to your SEO Meta Description."
msgstr ""

msgid "Make sure the focus keyword appears in the SEO description too."
msgstr ""

msgid "Focus Keyword used inside SEO Meta Description."
msgstr ""

msgid "Focus Keyword not found in your SEO Meta Description."
msgstr ""

msgid "Use Focus Keyword in the URL."
msgstr ""

msgid "Include the focus keyword in the slug (permalink) of this post."
msgstr ""

msgid "Focus Keyword used in the URL."
msgstr ""

msgid "Focus Keyword not found in the URL."
msgstr ""

msgid "Use Focus Keyword in subheading(s) like H2, H3, H4, etc.."
msgstr ""

msgid "It is recommended to add the focus keyword as part of one or more subheadings in the content."
msgstr ""

msgid "Focus Keyword found in the subheading(s)."
msgstr ""

msgid "Focus Keyword not found in subheading(s) like H2, H3, H4, etc.."
msgstr ""

msgid "Add Focus Keyword to the SEO title."
msgstr ""

msgid "Make sure the focus keyword appears in the SEO post title too."
msgstr ""

msgid "Hurray! You're using Focus Keyword in the SEO Title."
msgstr ""

msgid "Focus Keyword does not appear in the SEO title."
msgstr ""

msgid "Set a Focus Keyword for this content."
msgstr ""

msgid "We are searching in database."
msgstr ""

msgid "You haven't used this Focus Keyword before."
msgstr ""

msgid "You have %1$s this Focus Keyword."
msgstr ""

msgid "already used"
msgstr ""

msgid "Content is %1$d words long. Good job!"
msgstr ""

msgid "Content is %1$d words long. Consider using at least 600 words."
msgstr ""

msgid "Content should be %1$s long."
msgstr ""

msgid "Minimum recommended content length should be 600 words."
msgstr ""

msgid "URL unavailable. Add a short URL."
msgstr ""

msgid "Permalink should be at most 75 characters long."
msgstr ""

msgid "URL is %1$d characters long. Kudos!"
msgstr ""

msgid "URL is %1$d characters long. Consider shortening it."
msgstr ""

msgid "Link out to external resources."
msgstr ""

msgid "It helps visitors read more about a topic and prevents pogosticking."
msgstr ""

msgid "Great! You are linking to external resources."
msgstr ""

msgid "No outbound links were found. Link out to external resources."
msgstr ""

msgid "Add internal links in your content."
msgstr ""

msgid "Internal links decrease your bounce rate and improve SEO."
msgstr ""

msgid "You are linking to other resources on your website which is great."
msgstr ""

msgid "We couldn't find any internal links in your content."
msgstr ""

msgid "Add DoFollow links pointing to external resources."
msgstr ""

msgid "PageRank Sculpting no longer works. Your posts should have a mix of nofollow and DoFollow links."
msgstr ""

msgid "At least one external link with DoFollow found in your content."
msgstr ""

msgid "We found %1$d outbound links in your content and all of them are nofollow."
msgstr ""

msgid "Add a number to your title to improve CTR."
msgstr ""

msgid "Headlines with numbers are 36% more likely to generate clicks, according to research by Conductor."
msgstr ""

msgid "You are using a number in your SEO title."
msgstr ""

msgid "Your SEO title doesn't contain a number."
msgstr ""

msgid "Add %s to your title to increase CTR."
msgstr ""

msgid "Power Words are tried-and-true words that copywriters use to attract more clicks."
msgstr ""

msgid "Your title contains %1$d power word(s). Booyah!"
msgstr ""

msgid "Your title doesn't contain a %1$s. Add at least one."
msgstr ""

msgid "Titles with positive or negative sentiment work best for higher CTR."
msgstr ""

msgid "Headlines with a strong emotional sentiment (positive or negative) tend to receive more clicks."
msgstr ""

msgid "Your title has a positive or a negative sentiment."
msgstr ""

msgid "Your title doesn't contain a %1$s word."
msgstr ""

msgid "Use the Focus Keyword near the beginning of SEO title."
msgstr ""

msgid "The SEO page title should contain the Focus Keyword preferably at the beginning."
msgstr ""

msgid "Focus Keyword used at the beginning of SEO title."
msgstr ""

msgid "Focus Keyword doesn't appear at the beginning of SEO title."
msgstr ""

msgid "Use %1$s to optimise the  %2$s."
msgstr ""

msgid "You are using %1$s to optimise this %2$s."
msgstr ""

msgid "You are not using %1$s to optimise this %2$s."
msgstr ""

msgid "Reviews are disabled on this Product."
msgstr ""

msgid "Reviews are enabled for this Product. Good Job!"
msgstr ""

msgid "You are not using the Product Schema for this Product."
msgstr ""

msgid "You are using the Product Schema for this Product"
msgstr ""

msgid "FAQ Options"
msgstr ""

msgid "List Style"
msgstr ""

msgid "Title Wrapper"
msgstr ""

msgid "H2"
msgstr ""

msgid "H3"
msgstr ""

msgid "H4"
msgstr ""

msgid "H5"
msgstr ""

msgid "H6"
msgstr ""

msgid "P"
msgstr ""

msgid "DIV"
msgstr ""

msgid "Image Size"
msgstr ""

msgid "Styling Options"
msgstr ""

msgid "Title Wrapper CSS Class(es)"
msgstr ""

msgid "Content Wrapper CSS Class(es)"
msgstr ""

msgid "List CSS Class(es)"
msgstr ""

msgid "Add Image"
msgstr ""

msgid "Question…"
msgstr ""

msgid "Hide Question"
msgstr ""

msgid "Delete Question"
msgstr ""

msgid "Enter the answer to the question"
msgstr ""

msgid "Add New FAQ"
msgstr ""

msgid "More Info"
msgstr ""

msgid "Enter a step title"
msgstr ""

msgid "Hide Step"
msgstr ""

msgid "Delete Step"
msgstr ""

msgid "Add Step Image"
msgstr ""

msgid "Enter a step description"
msgstr ""

msgid "HowTo Options"
msgstr ""

msgid "Main Image Size"
msgstr ""

msgid "Step Title Wrapper CSS Class(es)"
msgstr ""

msgid "Step Content Wrapper CSS Class(es)"
msgstr ""

msgid "Step List CSS Class(es)"
msgstr ""

msgid "Add Final Image"
msgstr ""

msgid "Enter a main description"
msgstr ""

msgid "Total time:"
msgstr ""

msgid "DD"
msgstr ""

msgid "HH"
msgstr ""

msgid "MM"
msgstr ""

msgid "Optional, use first field to describe the duration."
msgstr ""

msgid "Add New Step"
msgstr ""

msgid "FAQ"
msgstr ""

msgid "Frequently Asked Questions"
msgstr ""

msgid "SEO"
msgstr ""

msgid "Structured Data"
msgstr ""

msgid "Yoast"
msgstr ""

msgid "Block"
msgstr ""

msgid "Markup"
msgstr ""

msgid "Rich Snippet"
msgstr ""

msgid "FAQ by Rank Math"
msgstr ""

msgid "Easily add Schema-ready, SEO-friendly, Frequently Asked Questions to your content."
msgstr ""

msgid "HowTo"
msgstr ""

msgid "HowTo by Rank Math"
msgstr ""

msgid "Easily add Schema-ready, SEO-friendly, HowTo block to your content."
msgstr ""

msgid "Schema by Rank Math"
msgstr ""

msgid "Add the Schema generated by Rank Math anywhere on your page using this Block."
msgstr ""

msgid "Lock Modified Date"
msgstr ""

msgid "Not available"
msgstr ""

msgid "Content is %1$d words long. Consider using at least 300 words."
msgstr ""

msgid "Minimum recommended content length should be 300 words."
msgstr ""

msgid "300 words"
msgstr ""

msgid "Edit Schema"
msgstr ""

msgid "Auto redirection created."
msgstr ""

msgid "Make Term Primary"
msgstr ""

msgid "Are you sure, you want to create a new GA4 Property?"
msgstr ""

msgid "Saving…"
msgstr ""

msgid "Some permissions are missing, please reconnect"
msgstr ""

msgid "Create new GA4 Property"
msgstr ""

msgid "Authorize"
msgstr ""

msgid "Search &hellip;"
msgstr ""

msgid "Search …"
msgstr ""

msgid "No results found"
msgstr ""

msgid "Passed Tests"
msgstr ""

msgid "Warnings"
msgstr ""

msgid "Failed Tests"
msgstr ""

msgid "Last checked: "
msgstr ""

msgid " at "
msgstr ""

msgid "Passed"
msgstr ""

msgid "Failed"
msgstr ""

msgid "Warning"
msgstr ""

msgid "Info"
msgstr ""

msgid "How to fix"
msgstr ""

msgid "Priority"
msgstr ""

msgid "Advanced SEO"
msgstr ""

msgid "Basic SEO"
msgstr ""

msgid "Performance"
msgstr ""

msgid "Security"
msgstr ""

msgid "Select / Deselect All"
msgstr ""

msgid "Use this file"
msgstr ""

msgid "Add or Upload File"
msgstr ""

msgid "Complete"
msgstr ""

msgid "Something went wrong! Please try again."
msgstr ""

msgid "Open in new tab."
msgstr ""

msgid "Set to nofollow."
msgstr ""

msgid "Set to sponsored."
msgstr ""

msgid "%s (opens in a new tab)"
msgstr ""

msgid "Warning: the link has been inserted but may have errors. Please test it."
msgstr ""

msgid "Link edited."
msgstr ""

msgid "Unlink"
msgstr ""

msgid "Link"
msgstr ""

msgid "Select Primary Term"
msgstr ""

msgid "Notify your readers by sharing!"
msgstr ""

msgid "Next steps…"
msgstr ""

msgid "Advanced Schema, Analytics and much more…"
msgstr ""

msgid "The plugin is currently not connected with your Rank Math account. Click on the button below to login or register for FREE using your "
msgstr ""

msgid "Google account, Facebook account"
msgstr ""

msgid " or "
msgstr ""

msgid "your email account."
msgstr ""

msgid "You have successfully activated Rank Math. If you find the plugin useful, "
msgstr ""

msgid "feel free to recommend it to your friends or colleagues."
msgstr ""

msgid "OnSite Checkout"
msgstr ""

msgid "You can't access this page."
msgstr ""

msgid "Failed! Try again"
msgstr ""

msgid "Saved"
msgstr ""

msgid "Warning: "
msgstr ""

msgid "Auto Update"
msgstr ""

msgid "You cannot turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released, because site wide plugins auto-update option is disabled on your site."
msgstr ""

msgid "Turn on auto-updates to automatically update to stable versions of Rank Math as soon as they are released. The beta versions of Rank Math as soon as they are released. The beta versions will never install automatically."
msgstr ""

msgid "Auto Update Plugin"
msgstr ""

msgid "When auto-updates are turned off, you can enable update notifications, to send an email to the site administrator when an update is available for Rank Math."
msgstr ""

msgid "Update Notification Email"
msgstr ""

msgid "Rank Math will not auto-update because you have rolled back to a previous version. Update to the latest version manually to make this option work again."
msgstr ""

msgid "Beta Opt-in"
msgstr ""

msgid "You cannot turn on the Beta Tester feature because site wide plugins auto-update option is disabled on your site."
msgstr ""

msgid "You can take part in shaping Rank Math by test-driving the newest features and letting us know what you think. Turn on the Beta Tester feature to get notified about new beta releases. The beta version will not install automatically and you always have the option to ignore it."
msgstr ""

msgid "It is not recommended to use the beta version on live production sites."
msgstr ""

msgid "Beta Tester"
msgstr ""

msgid "Rollback to Previous Version"
msgstr ""

msgid "If you are facing issues after an update, you can reinstall a previous version with this tool."
msgstr ""

msgid "Previous versions may not be secure or stable. Proceed with caution and always create a backup."
msgstr ""

msgid "Your Version"
msgstr ""

msgid "Rolled Back Version: "
msgstr ""

msgid "Auto updates will not work, please update the plugin manually."
msgstr ""

msgid "You are using the latest version of the plugin."
msgstr ""

msgid "This is the version you are using on this site."
msgstr ""

msgid "Latest Stable Version"
msgstr ""

msgid "Update Now"
msgstr ""

msgid "This is the latest version of the plugin."
msgstr ""

msgid "Rollback Version"
msgstr ""

msgid "Roll back to this version."
msgstr ""

msgid "Install Version "
msgstr ""

msgid "Reinstalling, please wait…"
msgstr ""

msgid "Import & Export"
msgstr ""

msgid "🔒 This is a PRO-Only Feature"
msgstr ""

msgid "We are sorry but this feature is only available to Rank Math PRO/Business/Agency Users. Unlock this feature and many more by getting a Rank Math plan."
msgstr ""

msgid "Bulk Edit SEO Tags"
msgstr ""

msgid "Advanced Google Analytics 4 Integration"
msgstr ""

msgid "Free Content AI Trial"
msgstr ""

msgid "SEO Performance Email Reports"
msgstr ""

msgid "⛔️ Update Required"
msgstr ""

msgid "🚀 Supercharge Your Content With AI"
msgstr ""

msgid "Start using Content AI by connecting your RankMath.com Account"
msgstr ""

msgid "To access this Content AI feature, you need to have an active subscription plan."
msgstr ""

msgid "To access this Content AI feature, you have to purchase a Content AI Subscription."
msgstr ""

msgid "You are one step away from unlocking this premium feature along with many more."
msgstr ""

msgid "1-Click Article Generation"
msgstr ""

msgid "Bulk Update Your SEO Meta using AI"
msgstr ""

msgid "Get Access to 40+ AI SEO Tools"
msgstr ""

msgid "125+ Expert-Written Prompts"
msgstr ""

msgid "1-Click Competitor Content Research"
msgstr ""

msgid "1-Click WooCommerce Product Descriptions"
msgstr ""

msgid "1-Click Competitor Research"
msgstr ""

msgid "On-Page SEO Suggestions"
msgstr ""

msgid "1-Click Bulk SEO Meta"
msgstr ""

msgid "125+ Pre-Built Prompts"
msgstr ""

msgid "Multiple RankBot Sessions"
msgstr ""

msgid "1-Click SEO Content"
msgstr ""

msgid "1-Click SEO Meta"
msgstr ""

msgid "40+ Specialized AI Tools"
msgstr ""

msgid "Gain access to 40+ advanced AI tools, empowering your content strategy."
msgstr ""

msgid "Experience the revolutionary AI-powered Content Editor for unparalleled efficiency."
msgstr ""

msgid "Engage with RankBot, your personal AI Chat Assistant, for real-time assistance."
msgstr ""

msgid "Server Maintenance Underway"
msgstr ""

msgid "We are working on improving your Content AI experience. Please wait for 5 minutes and then refresh to start using the optimized Content AI. If you see this for more than 5 minutes, please "
msgstr ""

msgid "reach out to the support team."
msgstr ""

msgid " We are sorry for the inconvenience."
msgstr ""

msgid "⛔️ Content AI Credit Alert!"
msgstr ""

msgid "Your monthly Content AI credits have been fully utilized. You can wait till %s for your credits to refresh or upgrade to continue enjoying seamless content creation"
msgstr ""

msgid "Your monthly Content AI credits have been fully utilized. To continue enjoying seamless content creation, simply click the button below to upgrade your plan and access more credits."
msgstr ""

msgid "Missing Credits?"
msgstr ""

msgid "Bulk Edit This Field"
msgstr ""

msgid "Save All Edits"
msgstr ""

msgid "This is what will appear in the first line when this post shows up in the search results."
msgstr ""

msgid "Permalink"
msgstr ""

msgid "Editing Homepage permalink is not possible."
msgstr ""

msgid "This is the unique URL of this page, displayed below the post title in the search results."
msgstr ""

msgid "This is what will appear as the description when this post shows up in the search results."
msgstr ""

msgid "Rating: "
msgstr ""

msgid "Preview"
msgstr ""

msgid "Desktop Preview"
msgstr ""

msgid "Mobile Preview"
msgstr ""

msgid "Videos"
msgstr ""

msgid "News"
msgstr ""

msgid "Maps"
msgstr ""

msgid "More"
msgstr ""

msgid "Tools"
msgstr ""

msgid "About 43,700,000 results (0.32 seconds) "
msgstr ""

msgid "Noindex robots meta is enabled"
msgstr ""

msgid "This page will not appear in search results. You can disable noindex in the Advanced tab."
msgstr ""

msgid "Replace Image"
msgstr ""

msgid "Remove Image"
msgstr ""

msgid "Upload at least 600x315px image. Recommended size is 1200x630px."
msgstr ""

msgid "Image is smaller than the minimum size, please select a different image."
msgstr ""

msgid "2 hrs"
msgstr ""

msgid "Customize the title, description and images of your post used while sharing on Facebook and Twitter."
msgstr ""

msgid "Add icon overlay to thumbnail"
msgstr ""

msgid "Icon overlay"
msgstr ""

msgid "You can add custom thumbnail overlays with {{link}}Rank Math Pro{{/link}}."
msgstr ""

msgid "App Description"
msgstr ""

msgid "You can use this as a more concise description than what you may have on the app store. This field has a maximum of 200 characters. (optional)"
msgstr ""

msgid "iPhone App Name"
msgstr ""

msgid "The name of your app to show."
msgstr ""

msgid "iPhone App ID"
msgstr ""

msgid "The numeric representation of your app ID in the App Store."
msgstr ""

msgid "iPhone App URL"
msgstr ""

msgid "Your app's custom URL scheme (must include ://)."
msgstr ""

msgid "iPad App Name"
msgstr ""

msgid "iPad App ID"
msgstr ""

msgid "iPad App URL"
msgstr ""

msgid "Google Play App Name"
msgstr ""

msgid "Google Play App ID"
msgstr ""

msgid "Your app ID in the Google Play (.i.e. com.android.app)"
msgstr ""

msgid "Google Play App URL"
msgstr ""

msgid "App Country"
msgstr ""

msgid "If your application is not available in the US App Store, you must set this value to the two-letter country code for the App Store that contains your application."
msgstr ""

msgid "Player URL"
msgstr ""

msgid "HTTPS URL to iFrame player. This must be a HTTPS URL which does not generate active mixed content warnings in a web browser. The audio or video player must not require plugins such as Adobe Flash."
msgstr ""

msgid "Player Size"
msgstr ""

msgid "iFrame width and height, specified in pixels in the following format: 600x400."
msgstr ""

msgid "Stream URL"
msgstr ""

msgid "Optional URL to raw stream that will be rendered in Twitter’s mobile applications directly. If provided, the stream must be delivered in the MPEG-4 container format (the .mp4 extension). The container can store a mix of audio and video with the following codecs: Video: H.264, Baseline Profile (BP), Level 3.0, up to 640 x 480 at 30 fps. Audio: AAC, Low Complexity Profile (LC)."
msgstr ""

msgid "Stream Content Type"
msgstr ""

msgid "The MIME type/subtype combination that describes the content contained in twitter:player:stream. Takes the form specified in RFC 6381. Currently supported content_type values are those defined in RFC 4337 (MIME Type Registration for MP4)."
msgstr ""

msgid "2h"
msgstr ""

msgid "The card for your website will look little something like this!"
msgstr ""

msgid "Use Data from Facebook Tab"
msgstr ""

msgid "Card Type"
msgstr ""

msgid "App Card"
msgstr ""

msgid "Player Card"
msgstr ""

msgid "Video clips and audio streams have a special place on the Twitter platform thanks to the Player Card. Player Cards must be submitted for approval before they can be used. More information: "
msgstr ""

msgid "The App Card is a great way to represent mobile applications on Twitter and to drive installs. More information: "
msgstr ""

msgid "Facebook"
msgstr ""

msgid "Twitter"
msgstr ""

msgid "Rate Rank Math SEO"
msgstr ""

msgid "Hey, we noticed you are using Rank Math SEO plugin for more than a week now –{{em}}that's awesome!{{/em}} Could you please do us a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr ""

msgid "I already did. Please don't show this message again."
msgstr ""

msgid "Social"
msgstr ""

msgid "Edit Snippet"
msgstr ""

msgid "Preview Snippet Editor"
msgstr ""

msgid "Close"
msgstr ""

msgid "Social Media Preview"
msgstr ""

msgid "Here you can view and edit the thumbnail, title and description that will be displayed when your site is shared on social media."
msgstr ""

msgid "Click on the button below to view and edit the preview."
msgstr ""

msgid "Preview & Edit Social Media"
msgstr ""

msgid "All Good"
msgstr ""

msgid "Errors"
msgstr ""

msgid "Additional"
msgstr ""

msgid "Title Readability"
msgstr ""

msgid "Content Readability"
msgstr ""

msgid "Read here to {{link}}Score 100/100{{/link}} "
msgstr ""

msgid "Activate your account by {{link}}connecting to Rank Math!{{/link}} "
msgstr ""

msgid "Want more? {{link}}{{strong}}Upgrade today to the PRO{{/strong}}{{/link}} version."
msgstr ""

msgid "Upgrade to re-order Focus Keywords"
msgstr ""

msgid "Improve the SEO workflow"
msgstr ""

msgid "Set different Primary Focus Keyword"
msgstr ""

msgid "and many other premium SEO features"
msgstr ""

msgid "Example: Rank Math SEO"
msgstr ""

msgid "Show Intent"
msgstr ""

msgid "Trends"
msgstr ""

msgid "Google Trends"
msgstr ""

msgid "Track Keyword Trends"
msgstr ""

msgid "Data fetched directly from Google"
msgstr ""

msgid "Analyze search trends and compare keywords"
msgstr ""

msgid "See data from a particular Country or timeframe"
msgstr ""

msgid "Insert keywords you want to rank for. Try to {{link}}attain 100/100 points{{/link}} for better chances of ranking."
msgstr ""

msgid "This post is Pillar Content"
msgstr ""

msgid "Instructs search engines to index and show these pages in the search results"
msgstr ""

msgid "Nofollow"
msgstr ""

msgid "This option prevents images on a page from being indexed by Google and other search engines"
msgstr ""

msgid "Destination URL"
msgstr ""

msgid "Canonical URL"
msgstr ""

msgid "The canonical URL informs search crawlers which page is the main page if you have double content"
msgstr ""

msgid "Breadcrumb Title"
msgstr ""

msgid "Breadcrumb Title to use for this post"
msgstr ""

msgid "Max Snippet"
msgstr ""

msgid "Specify a maximum text-length, in characters, of a snippet for your page"
msgstr ""

msgid "Max Video Preview"
msgstr ""

msgid "Specify a maximum duration in seconds of an animated video preview"
msgstr ""

msgid "Max Image Preview"
msgstr ""

msgid "Specify a maximum size of image preview to be shown for images on this page"
msgstr ""

msgid "Show SEO Score on Front-end"
msgstr ""

msgid "Connect FREE Account"
msgstr ""

msgid "By connecting your free account, you get keyword suggestions directly from Google when entering the focus keywords. Not only that, get access to our revolutionary Content AI, SEO Analyzer inside WordPress that scans your website for SEO errors and suggest improvements."
msgstr ""

msgid "Read more by following this link."
msgstr ""

msgid "Connect Your Account"
msgstr ""

msgid "Skip Step"
msgstr ""

msgid "Save and Continue"
msgstr ""

msgid "Easy"
msgstr ""

msgid "For websites where you only want to change the basics and let Rank Math do most of the heavy lifting. Most settings are set to default as per industry best practices. One just has to set it and forget it."
msgstr ""

msgid "For the advanced users who want to control every SEO aspect of the website. You are offered options to change everything and have full control over the website’s SEO."
msgstr ""

msgid "Custom Mode"
msgstr ""

msgid "Select this if you have a custom Rank Math settings file you want to use."
msgstr ""

msgid "You can easily switch between modes at any point."
msgstr ""

msgid "Your server is correctly configured to use Rank Math."
msgstr ""

msgid "Your server is correctly configured to use this plugin."
msgstr ""

msgid "Please resolve the issues above to be able to use all features of Rank Math plugin. If you are not sure how to do it, please contact your hosting provider."
msgstr ""

msgid "Please resolve the issues above to be able to use all SEO features. If you are not sure how to do it, please contact your hosting provider."
msgstr ""

msgid "You can import settings in the next step."
msgstr ""

msgid "Deactivated"
msgstr ""

msgid "Deactivate Plugin"
msgstr ""

msgid "No known conflicting plugins found."
msgstr ""

msgid "The following active plugins on your site may cause conflict issues when used alongside Rank Math: "
msgstr ""

msgid "The following active plugins on your site may cause conflict issues when used alongside this plugin: "
msgstr ""

msgid "You are using the recommended WordPress version."
msgstr ""

msgid "PHP DOM Extension"
msgstr ""

msgid "PHP SimpleXML Extension"
msgstr ""

msgid "PHP GD or Imagick Extension"
msgstr ""

msgid "PHP MBstring Extension"
msgstr ""

msgid "PHP OpenSSL Extension"
msgstr ""

msgid "Base64 encode & decode functions"
msgstr ""

msgid "available"
msgstr ""

msgid "installed"
msgstr ""

msgid "missing"
msgstr ""

msgid "Your PHP Version: %s | Recommended version: 7.4 | Minimal required: 7.2"
msgstr ""

msgid "Your PHP Version: %s"
msgstr ""

msgid "Rank Math is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security. "
msgstr ""

msgid "More information"
msgstr ""

msgid "This plugin is compatible with your PHP version but we recommend updating to PHP 7.4 for increased speed and security."
msgstr ""

msgid " | Recommended: PHP 7.4 or later"
msgstr ""

msgid "Your website is compatible to run Rank Math SEO"
msgstr ""

msgid "Less"
msgstr ""

msgid "Start Wizard"
msgstr ""

msgid "Recalculate SEO Scores"
msgstr ""

msgid "No posts found without SEO score."
msgstr ""

msgid "Importing: "
msgstr ""

msgid "% Completed"
msgstr ""

msgid "Import meta data from the %s plugin."
msgstr ""

msgid "Import settings and meta data from the %s plugin."
msgstr ""

msgid "The process may take a few minutes if you have a large number of posts or pages <a href=\"%1$s\">Learn more about the import process here.</a>"
msgstr ""

msgid "%s plugin will be disabled automatically moving forward to avoid conflicts. <strong>It is thus recommended to import the data you need now.</strong>"
msgstr ""

msgid "Input Data From:"
msgstr ""

msgid "Deactivating Plugins…"
msgstr ""

msgid "Skip, Don't Import Now"
msgstr ""

msgid "Please select plugin to import data."
msgstr ""

msgid "Continue"
msgstr ""

msgid "Start Import"
msgstr ""

msgid "Resetting…"
msgstr ""

msgid "Resetted"
msgstr ""

msgid "Updating…"
msgstr ""

msgid "Updated"
msgstr ""

msgid "Click here to learn how to setup Rank Math properly"
msgstr ""

msgid "Search the Knowledge Base for answers to your questions:"
msgstr ""

msgid "Type here to search…"
msgstr ""

msgid "Setup Tutorial"
msgstr ""

msgid "Knowledge Base"
msgstr ""

msgid "%1$s is a…"
msgstr ""

msgid "Select the type that best describes your business. If you can't find one that applies exactly, use the generic \"Organization\" or \"Local Business\" types."
msgstr ""

msgid "Logo for Google"
msgstr ""

msgid "<strong>Min Size: 112Χ112px</strong>.<br />A squared image is preferred by the search engines."
msgstr ""

msgid "Default Social Share Image"
msgstr ""

msgid "When a featured image or an OpenGraph Image is not set for individual posts/pages/CPTs, this image will be used as a fallback thumbnail when your post is shared on Facebook. <strong>The recommended image size is 1200 x 630 pixels.</strong>"
msgstr ""

msgid "If you are new to Rank Math,"
msgstr ""

msgid "to learn more."
msgstr ""

msgid "Enable this option to show the Index Status tab in the Analytics module. "
msgstr ""

msgid "Select Property"
msgstr ""

msgid "XML Sitemaps help search engines index your website&#039;s content more effectively."
msgstr ""

msgid "Include Images"
msgstr ""

msgid "Include reference to images from the post content in sitemaps. This helps search engines index your images better."
msgstr ""

msgid "Public Post Types"
msgstr ""

msgid "Select post types to enable SEO options for them and include them in the sitemap."
msgstr ""

msgid "Public Taxonomies"
msgstr ""

msgid "Select taxonomies to enable SEO options for them and include them in the sitemap."
msgstr ""

msgid "Automatically add a <code>target=\"_blank\"</code> attribute to external links appearing in your posts, pages, and other post types. The attributes are applied when the content is displayed, which does not change the stored content."
msgstr ""

msgid "Subscribe to Our YouTube Channel"
msgstr ""

msgid "Know more about the PRO version"
msgstr ""

msgid "Join FREE Facebook Group"
msgstr ""

msgid "Get 24x7 Support"
msgstr ""

msgid "Your site is now optimized"
msgstr ""

msgid "Proceed to Settings"
msgstr ""

msgid "Score 100"
msgstr ""

msgid "Return to dashboard"
msgstr ""

msgid "Proceed to Help Page"
msgstr ""

msgid "Setup Advanced Options"
msgstr ""

msgid "Your site is ready! "
msgstr ""

msgid "Enable auto update of the plugin"
msgstr ""

msgid "Set default values for the 404 error monitor here."
msgstr ""

msgid "The 404 monitor will let you see if visitors or search engines bump into any <code>404 Not Found</code> error while browsing your site."
msgstr ""

msgid "Set default values for the redirection module from here. "
msgstr ""

msgid "Learn more about Redirections."
msgstr ""

msgid "Set up temporary or permanent redirections. Combined with the 404 monitor, you can easily redirect faulty URLs on your site, or add custom redirections."
msgstr ""

msgid "Use automatic structured data to mark up content, to help Google better understand your content's context for display in Search. You can set different defaults for your posts here."
msgstr ""

msgid "Schema Type for %s"
msgstr ""

msgid "Default rich snippet selected when creating a new post of this type."
msgstr ""

msgid "Import"
msgstr ""

msgid "Import SEO Settings"
msgstr ""

msgid "You can import SEO settings from the following plugins:"
msgstr ""

msgid "Your Site"
msgstr ""

msgid "Your Website: %s"
msgstr ""

msgid "Let us know a few things about your site…"
msgstr ""

msgid "Connect Google&trade; Services"
msgstr ""

msgid "Rank Math automates everything, use below button to connect your site with Google Search Console and Google Analytics. It will verify your site and submit sitemaps automatically. "
msgstr ""

msgid "Read more about it here."
msgstr ""

msgid "Choose your Sitemap configuration and select which type of posts or pages you want to include in your Sitemaps."
msgstr ""

msgid "Optimization"
msgstr ""

msgid "SEO Tweaks"
msgstr ""

msgid "Automate some of your SEO tasks like making external links nofollow, redirecting attachment pages, etc."
msgstr ""

msgid "Ready"
msgstr ""

msgid "Advanced Options"
msgstr ""

msgid "Set capabilities here."
msgstr ""

msgid "404 + Redirection"
msgstr ""

msgid "Schema Markup"
msgstr ""

msgid "Schema adds metadata to your website, resulting in rich search results and more traffic."
msgstr ""

msgid "The below options are handled via the Rank Math SEO <settings_page_link/>."
msgstr ""

msgid "settings page"
msgstr ""

msgid "Save (Lock Modified Date)"
msgstr ""

msgid "Update (Lock Modified Date)"
msgstr ""

msgid "Content AI score"
msgstr ""

msgid "Content AI Score."
msgstr ""

msgid "Rank Math's SEO Score"
msgstr ""

msgid "Search Impressions"
msgstr ""

msgid "This is how many times your site showed up in the search results."
msgstr ""

msgid "This is the average position of your site in the search results."
msgstr ""

msgid "Index Status"
msgstr ""

msgid "URL Inspection Status"
msgstr ""

msgid "PageSpeed"
msgstr ""

msgid "Pro Feature"
msgstr ""

msgid "Delete from Keyword Manager"
msgstr ""

msgid "Google: "
msgstr ""

msgid "Available in the PRO version"
msgstr ""

msgid "Last Crawl: "
msgstr ""

msgid "PRO Feature"
msgstr ""

msgid "PRO Version offers Advanced Indexing Stats"
msgstr ""

msgid "Monitor metrics like Index Status, Last Crawl date, etc"
msgstr ""

msgid "All the Indexing statistics about your content in one place"
msgstr ""

msgid "Use data provided by Google instead of 3rd party tools"
msgstr ""

msgid "Unknown fetch state"
msgstr ""

msgid "Successful fetch"
msgstr ""

msgid "Soft 404"
msgstr ""

msgid "Blocked by robots.txt"
msgstr ""

msgid "Not found (404)"
msgstr ""

msgid "Blocked due to unauthorized request (401)"
msgstr ""

msgid "Server error (5xx)"
msgstr ""

msgid "Redirection error"
msgstr ""

msgid "Blocked due to access forbidden (403)"
msgstr ""

msgid "Blocked due to other 4xx issue (not 403, 404)"
msgstr ""

msgid "Internal error"
msgstr ""

msgid "Invalid URL"
msgstr ""

msgid "Unspecified"
msgstr ""

msgid "Excluded"
msgstr ""

msgid "No data to display. Check back later or try to update data manually from %s"
msgstr ""

msgid "Rank Math > General Settings > Analytics > Click 'Update data manually' button."
msgstr ""

msgid "Close Permanently"
msgstr ""

msgid "Hide Analytics Stats"
msgstr ""

msgid "Show Analytics Stats"
msgstr ""

msgid "Advanced Stats are available in the PRO version, %1$s."
msgstr ""

msgid "learn More"
msgstr ""

msgid "Are you sure you want to do this? This action will close the Stats bar permanently. Instead, you can use the toggle icon to minimize it."
msgstr ""

msgid "Suggested Actions"
msgstr ""

msgid "[Edit]"
msgstr ""

msgid "Timeframe"
msgstr ""

msgid "Search Clicks"
msgstr ""

msgid "#"
msgstr ""

msgid "Traffic"
msgstr ""

msgid "Prioritize Your Content Efforts With Detailed Insights"
msgstr ""

msgid "All the statistics about your content all in one place"
msgstr ""

msgid "Monitor key metrics like traffic and search performance"
msgstr ""

msgid "Content"
msgstr ""

msgid "Good Score"
msgstr ""

msgid "SEO score between 80 and 100. These posts are well optimized and usually do not require further actions."
msgstr ""

msgid "Fair Score"
msgstr ""

msgid "SEO score between 50 and 80. You may want to revisit these posts for further optimization."
msgstr ""

msgid "Poor Score"
msgstr ""

msgid "SEO score below 50. These posts are not well optimized and require further optimization."
msgstr ""

msgid "No Data"
msgstr ""

msgid "These posts have not been analyzed by Rank Math yet."
msgstr ""

msgid "Site Analytics"
msgstr ""

msgid "Good"
msgstr ""

msgid "Fair"
msgstr ""

msgid "Poor"
msgstr ""

msgid "Overall Optimization"
msgstr ""

msgid "is the average Rank Math’s SEO score. This chart shows how well your posts are optimized based on Rank Math’s scoring system."
msgstr ""

msgid "Open Report"
msgstr ""

msgid "How many times your site was clicked on in the search results."
msgstr ""

msgid "Avg. CTR"
msgstr ""

msgid "Average click-through rate. Search clicks divided by search impressions."
msgstr ""

msgid "Top Winning Keywords"
msgstr ""

msgid "Top Losing Keywords"
msgstr ""

msgid "Track Keyword Performance"
msgstr ""

msgid "Find winning keywords"
msgstr ""

msgid "Keep an eye on losing keywords"
msgstr ""

msgid "Built-in Keyword Rank Tracker"
msgstr ""

msgid "All Keywords"
msgstr ""

msgid "Tracked Keywords"
msgstr ""

msgid "10-50 Positions"
msgstr ""

msgid "Keyword Positions"
msgstr ""

msgid "Take full control of what’s important to the success of your website – see content that’s performing well and content that’s dropped in rankings so you can take action."
msgstr ""

msgid "Avg. Position"
msgstr ""

msgid "Impressions"
msgstr ""

msgid "Clicks"
msgstr ""

msgid "Position"
msgstr ""

msgid "Adsense"
msgstr ""

msgid "Pageviews"
msgstr ""

msgid "SEO Performance"
msgstr ""

msgid "Indexing Allowed"
msgstr ""

msgid "Rich Results"
msgstr ""

msgid "Page Fetch"
msgstr ""

msgid "Crawled As [PRO]"
msgstr ""

msgid "Robots state [PRO]"
msgstr ""

msgid "Pass"
msgstr ""

msgid "Error"
msgstr ""

msgid "Get Advanced Index Stats Directly from Google database."
msgstr ""

msgid "Upgrade to Rank Math PRO!"
msgstr ""

msgid "Your site appears in the best position for these keywords."
msgstr ""

msgid "Your site appears on the first page for these keywords, but not in the top 3 positions."
msgstr ""

msgid "Your site appears somewhere on pages 2-5 of the search results for these keywords."
msgstr ""

msgid "51-100 Positions"
msgstr ""

msgid "Your site appears in the search results for these keywords, but not on the first couple of pages."
msgstr ""

msgid "Rest of the Keywords"
msgstr ""

msgid "Prioritize what’s most important so you can take action before its too late by seeing keywords you’re ranking well for and keywords where your site’s position has dropped."
msgstr ""

msgid "Ready for more than just an overview? We have fully-fledged reports!"
msgstr ""

msgid "Track more than 20 metrics for all of your posts"
msgstr ""

msgid "Monitor Google trends for your selected focus keyword"
msgstr ""

msgid "Keep an eye on the data that matters all in one place"
msgstr ""

msgid "Single Post/Page Reports"
msgstr ""

msgid "Position History"
msgstr ""

msgid "Used"
msgstr ""

msgid "Remaining"
msgstr ""

msgid "Allowed"
msgstr ""

msgid "Your Own Keyword Manager"
msgstr ""

msgid "Track your performance for your target keywords"
msgstr ""

msgid "Monitor impressions, clicks, and position history"
msgstr ""

msgid "No additional monthly subscriptions for third-party tools"
msgstr ""

msgid "Keyword Manager"
msgstr ""

msgid "Rank Tracker"
msgstr ""

msgid "Last updated"
msgstr ""

msgid "Note:"
msgstr ""

msgid "The statistics that appear in the Rank Math Analytics module won’t match with the data from the Google Search Console as we only track posts and keywords that rank in the top 100 positions in the selected timeframe. We do this to help make decision-making easier and for faster data processing since this is the data you really need to prioritize your SEO efforts on."
msgstr ""

msgid "Failed to generate alt text."
msgstr ""

msgid "Generate Alt"
msgstr ""

msgid "Generating…"
msgstr ""

msgid "Generate Alt with AI"
msgstr ""

msgid "Blog Post Idea"
msgstr ""

msgid "Get fresh ideas for engaging blog posts that resonate with your niche and audience, ensuring captivating content."
msgstr ""

msgid "Describe Your Industry/Niche"
msgstr ""

msgid "e.g. Technology blog that covers latest gadgets, tech news, and reviews"
msgstr ""

msgid "Blog Post Outline"
msgstr ""

msgid "Structure blog posts with a clear flow, guiding readers effortlessly for better understanding and engagement."
msgstr ""

msgid "Blog Post Introduction"
msgstr ""

msgid "Craft attractive intros that captivate readers' interest, compelling them to explore further into your blog."
msgstr ""

msgid "Blog Post Conclusion"
msgstr ""

msgid "End your blog posts with impactful summaries, reinforcing key takeaways and leaving a lasting impression."
msgstr ""

msgid "Create eye-catching headlines for articles and blogs, grabbing readers' attention and boosting engagement."
msgstr ""

msgid "Topic Research"
msgstr ""

msgid "Dive deep into comprehensive reports on specific topics, uncovering trends, history, and industry players."
msgstr ""

msgid "Optimize headlines for enhanced visibility, organic traffic, and a stronger online presence."
msgstr ""

msgid "Craft concise and persuasive summaries that captivate readers and search engines, improving click-through rates."
msgstr ""

msgid "Paragraph"
msgstr ""

msgid "Generate well-structured and informative paragraphs, seamlessly blending into your content for better readability."
msgstr ""

msgid "Paragraph Rewriter"
msgstr ""

msgid "Refine paragraphs while preserving meaning, ensuring originality, and enhancing clarity."
msgstr ""

msgid "Sentence Expander"
msgstr ""

msgid "Transform incomplete sentences into polished expressions, adding depth and clarity to your writing."
msgstr ""

msgid "Text Summarizer"
msgstr ""

msgid "Condense complex texts into concise summaries, highlighting crucial points and essential information."
msgstr ""

msgid "Fix Grammar"
msgstr ""

msgid "Utilize AI-powered grammar correction to polish your written content, eliminating errors and improving clarity."
msgstr ""

msgid "Text"
msgstr ""

msgid "Enter the text to fix grammar"
msgstr ""

msgid "Analogy"
msgstr ""

msgid "Enhance clarity by rephrasing text using alternative words, providing a fresh perspective without altering meaning."
msgstr ""

msgid "Product Description"
msgstr ""

msgid "Craft compelling descriptions that effectively showcase the unique benefits and features of your product."
msgstr ""

msgid "Product Pros & Cons"
msgstr ""

msgid "Present balanced overviews outlining the advantages and limitations, aiding informed decisions."
msgstr ""

msgid "Product Review"
msgstr ""

msgid "Provide detailed evaluations covering strengths, weaknesses, and practical recommendations."
msgstr ""

msgid "Address common queries with comprehensive answers, offering valuable information and guidance."
msgstr ""

msgid "Comment Reply"
msgstr ""

msgid "Engage your audience with thoughtful and engaging responses, fostering meaningful interactions."
msgstr ""

msgid "Personal Bio"
msgstr ""

msgid "Create professional and captivating biographies highlighting accomplishments, expertise, and personality."
msgstr ""

msgid "Company Bio"
msgstr ""

msgid "Craft informative overviews of your company's history, values, mission, and team, building credibility."
msgstr ""

msgid "Job Description"
msgstr ""

msgid "Create enticing and comprehensive descriptions outlining requirements, responsibilities, and opportunities."
msgstr ""

msgid "Testimonial"
msgstr ""

msgid "Develop persuasive testimonials sharing positive experiences, endorsing your product, service, or brand."
msgstr ""

msgid "Product or Service"
msgstr ""

msgid "Facebook Post"
msgstr ""

msgid "Create intriguing and shareable content for Facebook, captivating your audience and boosting engagement."
msgstr ""

msgid "Facebook Comment Reply"
msgstr ""

msgid "Generate relevant responses to Facebook comments, build relationships & encourage interaction."
msgstr ""

msgid "Reply brief"
msgstr ""

msgid "Post brief"
msgstr ""

msgid "Create engaging tweets, boost interaction, and foster connections with your followers."
msgstr ""

msgid "Tweet Reply"
msgstr ""

msgid "Generate optimized replies for tweets to promote engagement and strengthen connections."
msgstr ""

msgid "Instagram Caption"
msgstr ""

msgid "Craft catchy captions for Instagram posts to increase engagement and grab attention."
msgstr ""

msgid "Create effective emails for promotions, announcements, and follow-ups to achieve marketing goals."
msgstr ""

msgid "Email Reply"
msgstr ""

msgid "Craft courteous email replies to promote interaction and strengthen relationships."
msgstr ""

msgid "AIDA"
msgstr ""

msgid "Write persuasive text using the Attention-Interest-Desire-Action formula to drive action."
msgstr ""

msgid "IDCA"
msgstr ""

msgid "Create compelling messages using the Identify-Develop-Communicate-Ask strategy to resonate."
msgstr ""

msgid "PAS"
msgstr ""

msgid "Address customer problems with the Problem-Agitate-Solution technique to fulfill needs."
msgstr ""

msgid "HERO"
msgstr ""

msgid "Craft captivating headlines using the HERO formula to engage, reveal, and offer value."
msgstr ""

msgid "SPIN"
msgstr ""

msgid "Describe customer problems, highlight implications, and offer solutions using the SPIN method."
msgstr ""

msgid "BAB"
msgstr ""

msgid "Create a compelling Before-After-Bridge narrative to demonstrate product or service value."
msgstr ""

msgid "YouTube Video Script"
msgstr ""

msgid "Develop engaging video scripts for YouTube to inform, entertain, and align."
msgstr ""

msgid "YouTube Video Description"
msgstr ""

msgid "Generate informative and engaging video descriptions for YouTube."
msgstr ""

msgid "Podcast Episode Outline"
msgstr ""

msgid "Create detailed outlines for podcast episodes, including topics and takeaways."
msgstr ""

msgid "Create detailed and easy-to-follow recipes with ingredients, instructions, and nutrition."
msgstr ""

msgid "Freeform Writing"
msgstr ""

msgid "Generate text based on prompts or topics, allowing for imaginative or technical writing."
msgstr ""

msgid "What do you want to write?"
msgstr ""

msgid "AI Command"
msgstr ""

msgid "Ask AI anything and receive relevant and informative responses for questions or requests."
msgstr ""

msgid "SEO Meta"
msgstr ""

msgid "Optimize headlines and descriptions to improve visibility on search engines."
msgstr ""

msgid "Open Graph"
msgstr ""

msgid "Boost content visibility on social media with topic-specific meta tags for easy discovery."
msgstr ""

msgid "Topic Brief"
msgstr ""

msgid "Enter a short summary of your topic"
msgstr ""

msgid "Audience"
msgstr ""

msgid "The target audience for the content."
msgstr ""

msgid "Select or Write Custom"
msgstr ""

msgid "Tone"
msgstr ""

msgid "The tone of the content."
msgstr ""

msgid "Style"
msgstr ""

msgid "The style of the content."
msgstr ""

msgid "Output Language"
msgstr ""

msgid "Topic"
msgstr ""

msgid "Main points &amp; ideas"
msgstr ""

msgid "Enter the main points you want to cover, separated by comma"
msgstr ""

msgid "Enter the main keywords to focus on"
msgstr ""

msgid "Post title"
msgstr ""

msgid "Enter your post title"
msgstr ""

msgid "Main Argument"
msgstr ""

msgid "Enter the main point you want to make"
msgstr ""

msgid "Call to Action"
msgstr ""

msgid "Post Brief"
msgstr ""

msgid "Enter a short summary of your post"
msgstr ""

msgid "Length"
msgstr ""

msgid "Relevance"
msgstr ""

msgid "Select or Write desired output format"
msgstr ""

msgid "Enter your SEO title"
msgstr ""

msgid "Supporting Points"
msgstr ""

msgid "The supporting points you want to include in the paragraph"
msgstr ""

msgid "Original Paragraph"
msgstr ""

msgid "Enter the paragraph you want to rephrase"
msgstr ""

msgid "Sentence"
msgstr ""

msgid "Enter a short or incomplete sentence"
msgstr ""

msgid "Original Text"
msgstr ""

msgid "Enter the text to summarize"
msgstr ""

msgid "Product Name"
msgstr ""

msgid "Enter the name of the product"
msgstr ""

msgid "Features and Benefits"
msgstr ""

msgid "Enter a list of features and benefits, separated by commas"
msgstr ""

msgid "Limitations and Drawbacks"
msgstr ""

msgid "Reply Brief"
msgstr ""

msgid "Enter a short summary of the required response"
msgstr ""

msgid "Original Comment"
msgstr ""

msgid "The original comment that requires a response"
msgstr ""

msgid "Personal Information"
msgstr ""

msgid "Enter personal details, such as your name, age, occupation, etc."
msgstr ""

msgid "Purpose"
msgstr ""

msgid "What is the purpose of this bio?"
msgstr ""

msgid "Personal Achievements"
msgstr ""

msgid "Enter a list of your personal achievements, separated by commas"
msgstr ""

msgid "Company Name"
msgstr ""

msgid "Enter the name of your company"
msgstr ""

msgid "Company Information"
msgstr ""

msgid "Enter company details, such as the company name, location, and industry"
msgstr ""

msgid "Company History"
msgstr ""

msgid "Enter a brief history of the company"
msgstr ""

msgid "Team"
msgstr ""

msgid "Enter a brief description of the team"
msgstr ""

msgid "Enter the job title."
msgstr ""

msgid "Requirements"
msgstr ""

msgid "Enter the key requirements for the position, separated by commas"
msgstr ""

msgid "Responsibilities"
msgstr ""

msgid "Enter a list of responsibilities, separated by commas"
msgstr ""

msgid "Comment"
msgstr ""

msgid "The comment you want to reply to."
msgstr ""

msgid "Hashtags"
msgstr ""

msgid "Enter one or more hashtags, separated by commas"
msgstr ""

msgid "Enter the original tweet to reply to."
msgstr ""

msgid "Email Brief"
msgstr ""

msgid "Enter a brief description of the email"
msgstr ""

msgid "Enter the original email"
msgstr ""

msgid "Introduce your product here. Provide a detailed description of its features and benefits, highlighting what sets it apart from competitors and why it's the perfect solution for your target audience."
msgstr ""

msgid "Visual Elements"
msgstr ""

msgid "Enter the visual elements you want to include in the video"
msgstr ""

msgid "Key Points"
msgstr ""

msgid "Enter the main points you want to cover, separated by commas."
msgstr ""

msgid "Host"
msgstr ""

msgid "Enter the name of the host of the podcast"
msgstr ""

msgid "Guest(s) or co-host"
msgstr ""

msgid "Enter the name(s) separated by comma"
msgstr ""

msgid "e.g. Italian, Chinese, Mexican"
msgstr ""

msgid "Type of Dish"
msgstr ""

msgid "e.g. soup, salad, casserole"
msgstr ""

msgid "Ingredients"
msgstr ""

msgid "Enter the ingredients needed for the recipe, separated by commas (e.g. flour, sugar, eggs, milk)"
msgstr ""

msgid "Dietary restrictions"
msgstr ""

msgid "List any dietary restrictions that the recipe should adhere to (e.g. gluten-free, vegan, low-carb)"
msgstr ""

msgid "Command"
msgstr ""

msgid "Enter your command"
msgstr ""

msgid "Instructions"
msgstr ""

msgid "Enter instructions"
msgstr ""

msgid "Document Title"
msgstr ""

msgid "Enter the document title"
msgstr ""

msgid "Sorry, the request has failed. If the issue persists, please contact our Support for assistance."
msgstr ""

msgid "Words: %d"
msgstr ""

msgid "Copy"
msgstr ""

msgid "Copied!"
msgstr ""

msgid "Insert"
msgstr ""

msgid "Learn how to use this %s Tool effectively."
msgstr ""

msgid "Click Here"
msgstr ""

msgid "1 Word Output = 1 Credit"
msgstr ""

msgid "Required fields."
msgstr ""

msgid "Outputs"
msgstr ""

msgid "Generate"
msgstr ""

msgid "Generate More"
msgstr ""

msgid "Output"
msgstr ""

msgid "Suggestions will appear here."
msgstr ""

msgid "%s to access all the Content AI tools"
msgstr ""

msgid "Click here"
msgstr ""

msgid "Thank you for choosing Rank Math! {{strong}}Enjoy 750 Credits monthly for life{{/strong}} as a token of our appreciation! 🎁"
msgstr ""

msgid "Marketing & Sales"
msgstr ""

msgid "eCommerce"
msgstr ""

msgid "Misc"
msgstr ""

msgid "Use"
msgstr ""

msgid "Regenerate"
msgstr ""

msgid "Write More"
msgstr ""

msgid "Dismiss"
msgstr ""

msgid "{{link}}Click here{{/link}} to learn how to use it."
msgstr ""

msgid "Recent"
msgstr ""

msgid "Custom +"
msgstr ""

msgid "Prompts Library"
msgstr ""

msgid "Updating..."
msgstr ""

msgid "Update Library"
msgstr ""

msgid "Prompt List"
msgstr ""

msgid "Add Custom Prompt +"
msgstr ""

msgid "Prompt Name"
msgstr ""

msgid "Prompt Text"
msgstr ""

msgid "Use [brackets] to insert placeholders."
msgstr ""

msgid "Save Prompt"
msgstr ""

msgid "Use Prompt"
msgstr ""

msgid "Your Personal Assistant"
msgstr ""

msgid "Know more about RankBot"
msgstr ""

msgid "Delete Session"
msgstr ""

msgid "New Chat"
msgstr ""

msgid "Buy PRO plan for Multiple Sessions"
msgstr ""

msgid "Examples"
msgstr ""

msgid "Here are some examples of questions you can ask RankBot"
msgstr ""

msgid "You:"
msgstr ""

msgid "RankBot:"
msgstr ""

msgid "Type your message here…"
msgstr ""

msgid "Regenerate Response"
msgstr ""

msgid "2000"
msgstr ""

msgid "Send"
msgstr ""

msgid "Copy as Text"
msgstr ""

msgid "Copy as Blocks"
msgstr ""

msgid "Create New Post"
msgstr ""

msgid "Expand Sidebar"
msgstr ""

msgid "Write"
msgstr ""

msgid "Collapse Sidebar"
msgstr ""

msgid "AI History"
msgstr ""

msgid "Words:"
msgstr ""

msgid "No AI History found."
msgstr ""

msgid "Run as Command"
msgstr ""

msgid "Improve"
msgstr ""

msgid "Summarize"
msgstr ""

msgid "Write Analogy"
msgstr ""

msgid "Content AI Commands"
msgstr ""

msgid "Shorten with AI"
msgstr ""

msgid "Image URL is missing."
msgstr ""

msgid "50 Content AI credits will be used to generate the Alt"
msgstr ""

msgid "Generate With AI"
msgstr ""

msgid "Generate Answer with Content AI"
msgstr ""

msgid "Click or Press Enter"
msgstr ""

msgid "Type / to choose a block or // to use Content AI"
msgstr ""

msgid "Back"
msgstr ""

msgid "Upgrade to buy more credits from "
msgstr ""

msgid "Content AI Pricing."
msgstr ""

msgid "here."
msgstr ""

msgid "Suggested length 2-3 Words"
msgstr ""

msgid "To learn how to use it"
msgstr ""

msgid "Refresh will use 500 Credit."
msgstr ""

msgid "500 credits will be used."
msgstr ""

msgid "Research"
msgstr ""

msgid "Score:"
msgstr ""

msgid "100"
msgstr ""

msgid "to"
msgstr ""

msgid "Copied"
msgstr ""

msgid "Ad Competition:"
msgstr ""

msgid "CPC:"
msgstr ""

msgid "Volume:"
msgstr ""

msgid "Click to copy keyword"
msgstr ""

msgid "No data available"
msgstr ""

msgid "Use Keyword in"
msgstr ""

msgid "Headings"
msgstr ""

msgid "Know more about Keywords."
msgstr ""

msgid "Click on any keyword to copy it."
msgstr ""

msgid "Please use only one or two keywords from here."
msgstr ""

msgid "There are no recommended Keywords for this researched keyword."
msgstr ""

msgid "Related Keywords"
msgstr ""

msgid "There are no recommended Questions for this researched keyword."
msgstr ""

msgid "Related Questions"
msgstr ""

msgid "Know more about Questions."
msgstr ""

msgid "Copy this data as a FAQ Block."
msgstr ""

msgid "Click on any question to copy it."
msgstr ""

msgid "There are no recommended Links for this researched keyword."
msgstr ""

msgid "Related External Links"
msgstr ""

msgid "Know more about Links."
msgstr ""

msgid "Use some of these external links in the content area. It is recommended to add"
msgstr ""

msgid "about or mention Schema."
msgstr ""

msgid "Questions"
msgstr ""

msgid "Fetching Search Results"
msgstr ""

msgid "Analysing Your Competitors"
msgstr ""

msgid "Crunching the Numbers"
msgstr ""

msgid "Cooking a Personalized SEO Plan"
msgstr ""

msgid "Final Touches to the SEO Recommendations"
msgstr ""

msgid "Content AI Knowledge Base."
msgstr ""

msgid "AI Fix was applied only considering the beginning of the content, as the full content is too large to be processed by the AI."
msgstr ""

msgid "Error: The request payload is too large!"
msgstr ""

msgid "Generate with AI"
msgstr ""

msgid "Blog Post Wizard"
msgstr ""

msgid "Create a complete blog post in one go. Just fill in some details and Content AI will create a complete blog post for you."
msgstr ""

msgid "Skip"
msgstr ""

msgid "Write Post"
msgstr ""

msgid "Next Step"
msgstr ""

msgid "Post Outline"
msgstr ""

msgid "Steps"
msgstr ""

msgid "Multistep Wizard"
msgstr ""

msgid "Prompt with this name already exists"
msgstr ""

msgid "Credits"
msgstr ""

msgid "Know more about credits."
msgstr ""

msgid "Content is %1$s words long. Consider using at least %2$s words."
msgstr ""

msgid "Minimum recommended content length should be %1$d words."
msgstr ""

msgid " words"
msgstr ""

msgid "Snippet Editor"
msgstr ""

msgid "Post Editor"
msgstr ""

msgid "Generated Content"
msgstr ""

msgid "Previous"
msgstr ""

msgid "Next"
msgstr ""

msgid "The highlighted content has been generated by AI for your review. Carefully evaluate the suggested changes and use the options below to accept or reject them."
msgstr ""

msgid "Approve"
msgstr ""

msgid "Reject"
msgstr ""

msgid "Undo"
msgstr ""

msgid "Fix with AI"
msgstr ""

msgid "Improve your content with a personal Content AI."
msgstr ""

msgid "Improve Now"
msgstr ""

msgid "You have exhausted your Content AI Credits."
msgstr ""

msgid "Get more"
msgstr ""

msgid "You do not have a Content AI plan."
msgstr ""

msgid "Choose your plan"
msgstr ""

msgid "Start using Content AI by connecting your RankMath account."
msgstr ""

msgid "This field must not be empty."
msgstr ""

msgid "Maintenance Code"
msgstr ""

msgid "Update Redirection"
msgstr ""

msgid "Add Redirection"
msgstr ""

msgid "Source URLs"
msgstr ""

msgid "Ignore Case"
msgstr ""

msgid "Toggle All"
msgstr ""

msgid "Control which user has access to which options of Rank Math."
msgstr ""

msgid "Reset"
msgstr ""

msgid "Update Capabilities"
msgstr ""

msgid "Author Name"
msgstr ""

msgid "Image URL"
msgstr ""

msgid "Rating"
msgstr ""

msgid "Rating score"
msgstr ""

msgid "Rating Minimum"
msgstr ""

msgid "Rating minimum score"
msgstr ""

msgid "Rating Maximum"
msgstr ""

msgid "Rating maximum score"
msgstr ""

msgid "Review"
msgstr ""

msgid "Either a specific edition of the written work, or the volume of the work"
msgstr ""

msgid "The title of the tome. Use for the title of the tome if it differs from the book. *Optional when tome has the same title as the book"
msgstr ""

msgid "The edition of the book"
msgstr ""

msgid "The ISBN of the print book"
msgstr ""

msgid "URL specific to this edition if one exists"
msgstr ""

msgid "Date of first publication of this tome"
msgstr ""

msgid "Book Format"
msgstr ""

msgid "Course Schedule"
msgstr ""

msgid "Suggested pacing in repeatFrequency units (8601 duration format). For example, PT5H monthly means 5 hours per month. Use courseWorkload for total length."
msgstr ""

msgid "The numerical value for how long the course lasts for, in repeatFrequency units. For example, if the repeatFrequency is monthly and the repeatCount is 4, the course lasts for 4 months."
msgstr ""

msgid "The duration and repeatCount properties are relative to this field. "
msgstr ""

msgid "Select Repeat Frequency"
msgstr ""

msgid "Daily"
msgstr ""

msgid "Weekly"
msgstr ""

msgid "Monthly"
msgstr ""

msgid "Yearly"
msgstr ""

msgid "The start date for the course, in 8601 date format (YYYY-MM-DD), if applicable."
msgstr ""

msgid "The end date for the course, in 8601 date format (YYYY-MM-DD), if applicable."
msgstr ""

msgid "Course Instance"
msgstr ""

msgid "The medium through which the course will be delivered."
msgstr ""

msgid "Total time to watch all videos and complete all assignments and exams for the course. Use the 8601 format. Example: PT22H"
msgstr ""

msgid "Offers"
msgstr ""

msgid "Category"
msgstr ""

msgid "Offer availability"
msgstr ""

msgid "Price Valid From"
msgstr ""

msgid "The date when the item becomes valid."
msgstr ""

msgid "The date after which the price will no longer be available"
msgstr ""

msgid "Inventory Level"
msgstr ""

msgid "The URL of the online event, where people can join. This property is required if your event is happening online"
msgstr ""

msgid "Street Address"
msgstr ""

msgid "Locality"
msgstr ""

msgid "Region"
msgstr ""

msgid "Postal Code"
msgstr ""

msgid "Country"
msgstr ""

msgid "The venue name."
msgstr ""

msgid "Website URL of the venue"
msgstr ""

msgid "Performer Information"
msgstr ""

msgid "Website or Social Link"
msgstr ""

msgid "Salary (Recommended)"
msgstr ""

msgid "Insert amount, e.g. 50.00, or a salary range, e.g. 40.00-50.00"
msgstr ""

msgid "Payroll (Recommended)"
msgstr ""

msgid "Salary amount is for"
msgstr ""

msgid "ISO 4217 Currency code. Example: EUR"
msgstr ""

msgid "Hiring Organization"
msgstr ""

msgid "The name of the company. Leave empty to use your own company information."
msgstr ""

msgid "Organization URL (Recommended)"
msgstr ""

msgid "The URL of the organization offering the job position. Leave empty to use your own company information"
msgstr ""

msgid "Organization Logo (Recommended)"
msgstr ""

msgid "Logo URL of the organization offering the job position. Leave empty to use your own company information"
msgstr ""

msgid "Brand Name"
msgstr ""

msgid "The number of calories in the recipe. Optional."
msgstr ""

msgid "A recipe video Name"
msgstr ""

msgid "A recipe video Description"
msgstr ""

msgid "Video URL"
msgstr ""

msgid "A video URL. Optional."
msgstr ""

msgid "Content URL"
msgstr ""

msgid "A URL pointing to the actual video media file"
msgstr ""

msgid "A recipe video thumbnail URL"
msgstr ""

msgid "ISO 8601 duration format. Example: PT1H30M"
msgstr ""

msgid "Video Upload Date"
msgstr ""

msgid "Instruction name of the recipe."
msgstr ""

msgid "Instruction Texts"
msgstr ""

msgid "Timings"
msgstr ""

msgid "Open Days"
msgstr ""

msgid "Google does not allow Person as the Publisher for articles. Organization will be used instead."
msgstr ""

msgid "Article Section"
msgstr ""

msgid "Review Location"
msgstr ""

msgid "The review or rating must be displayed on the page to comply with Google's Schema guidelines."
msgstr ""

msgid "You can either use this shortcode or Schema Block in the block editor to print the schema data in the content in order to meet the Google's guidelines. Read more about it <a href=https://developers.google.com/search/docs/guides/sd-policies#content target=_blank>here</a>."
msgstr ""

msgid "Editions"
msgstr ""

msgid "The pricing category of the course. Example: Free, Partially Free, Subscription, Paid"
msgstr ""

msgid "The numerical price of the course, if applicable."
msgstr ""

msgid "The currency of the price of the course, in ISO 4217 currency format (3 letter code), if applicable."
msgstr ""

msgid "Type of the event"
msgstr ""

msgid "Current status of the event (optional)"
msgstr ""

msgid "Indicates whether the event occurs online, offline at a physical location, or a mix of both online and offline."
msgstr ""

msgid "Date and time of the event"
msgstr ""

msgid "End date and time of the event"
msgstr ""

msgid "The original date on which employer posted the job. You can leave it empty to use the post publication date as job posted date"
msgstr ""

msgid "The date when the job posting will expire. If a job posting never expires, or you do not know when the job will expire, do not include this property"
msgstr ""

msgid "If checked, post status will be changed to Draft and its URL will return a 404 error, as required by the Rich Result guidelines"
msgstr ""

msgid "Employment Type (Recommended)"
msgstr ""

msgid "Type of employment. You can choose more than one value"
msgstr ""

msgid "Posting ID (Recommended)"
msgstr ""

msgid "The hiring organization's unique identifier for the job."
msgstr ""

msgid "Music Type"
msgstr ""

msgid "Job title"
msgstr ""

msgid "The job title of the person (for example, Financial Manager)."
msgstr ""

msgid "Product name"
msgstr ""

msgid "Gtin"
msgstr ""

msgid "MPN"
msgstr ""

msgid "Type of dish, for example appetizer, or dessert."
msgstr ""

msgid "The cuisine of the recipe (for example, French or Ethiopian)."
msgstr ""

msgid "Other terms for your recipe such as the season, the holiday, or other descriptors. Separate multiple entries with commas."
msgstr ""

msgid "Quantity produced by the recipe, for example 4 servings"
msgstr ""

msgid "Recipe ingredients, add one item per line"
msgstr ""

msgid "Instruction Type"
msgstr ""

msgid "URL pointing to the menu of the restaurant."
msgstr ""

msgid "The type of service being offered, e.g. veterans' benefits, emergency relief, etc."
msgstr ""

msgid "Software"
msgstr ""

msgid "For example, Windows 7, OSX 10.6, Android 1.6"
msgstr ""

msgid "For example, Game, Multimedia"
msgstr ""

msgid "Upload Date"
msgstr ""

msgid "Embed URL"
msgstr ""

msgid "A URL pointing to the embeddable player for the video. Example: <code>https://www.youtube.com/embed/VIDEOID</code>"
msgstr ""

msgid "A URL pointing to the actual video media file like MP4, MOV, etc. Please leave it empty if you don't know the URL."
msgstr ""

msgid "ISO 8601 duration format. Example: 1H30M"
msgstr ""

msgid "Video Thumbnail"
msgstr ""

msgid "A video thumbnail URL"
msgstr ""

msgid "Podcast Episode"
msgstr ""

msgid "Dataset"
msgstr ""

msgid "Fact Check"
msgstr ""

msgid "Movie"
msgstr ""

msgid "Delete?"
msgstr ""

msgid "Yes"
msgstr ""

msgid "No"
msgstr ""

msgid "Schema in Use"
msgstr ""

msgid "Multiple Schemas are allowed in the"
msgstr ""

msgid "PRO Version"
msgstr ""

msgid "Add Property"
msgstr ""

msgid "Add Property Group"
msgstr ""

msgid "Duplicate Group"
msgstr ""

msgid "Advanced Editor"
msgstr ""

msgid "Each save will create a new template."
msgstr ""

msgid "Template saved."
msgstr ""

msgid "Save as Template"
msgstr ""

msgid "Saving"
msgstr ""

msgid "Save for this Term"
msgstr ""

msgid "Save for this Post"
msgstr ""

msgid "Are you sure you want to convert? You can't use simple mode for this edited Schema."
msgstr ""

msgid "JSON-LD Code"
msgstr ""

msgid "Test with Google"
msgstr ""

msgid "Preview & Validate Your Schema Markup"
msgstr ""

msgid "Advanced Schema markup viewer"
msgstr ""

msgid "Live testing with Google"
msgstr ""

msgid "No other SEO plugin offers this feature"
msgstr ""

msgid "Code Validation"
msgstr ""

msgid "Schema Builder"
msgstr ""

msgid "Pro"
msgstr ""

msgid "Available Schema Types"
msgstr ""

msgid "Schema Catalog"
msgstr ""

msgid "Search…"
msgstr ""

msgid "Advanced Schema Builder"
msgstr ""

msgid "Possibility to create 700+ Schema Types"
msgstr ""

msgid "Import Schema from ANY website"
msgstr ""

msgid "Create Advanced templates"
msgstr ""

msgid "Schema Templates"
msgstr ""

msgid "Custom Schema"
msgstr ""

msgid "Schema Generator"
msgstr ""

msgid "Configure Schema Markup for your pages. Search engines, use structured data to display rich results in SERPs."
msgstr ""

msgid "Select Schema"
msgstr ""

msgid "Heading text"
msgstr ""

msgid "Heading Link"
msgstr ""

msgid "Edit Link"
msgstr ""

msgid "Exclude Headings"
msgstr ""

msgid "Heading "
msgstr ""

msgid "Table of Content Options"
msgstr ""

msgid "Unordered List"
msgstr ""

msgid "Ordered List"
msgstr ""

msgid "Add Heading blocks to this page to generate the Table of Contents."
msgstr ""

msgid "Enter a title"
msgstr ""

msgid "Change URL"
msgstr ""

msgid "Analysing Page…"
msgstr ""

msgid "Analysing Website…"
msgstr ""

msgid "Start Page Analysis"
msgstr ""

msgid "Start SEO Analyzer"
msgstr ""

msgid "Restart SEO Analyzer"
msgstr ""

msgid "Print"
msgstr ""

msgid "View Issues"
msgstr ""

msgid "SEO Analysis for"
msgstr ""

msgid "Analyze your site by "
msgstr ""

msgid "linking your Rank Math account"
msgstr ""

msgid "Competitor Analyzer"
msgstr ""

msgid "Enter a site URL to see how it ranks for the same SEO criteria as site."
msgstr ""

msgid "Analyze competitor websites to gain an edge"
msgstr ""

msgid "Evaluate strengths and weaknesses"
msgstr ""

msgid "Explore new keywords and opportunities"
msgstr ""

msgid "Make more informed decisions & strategy"
msgstr ""

msgid "Starting SEO score recalculation"
msgstr ""

msgid "The SEO Scores have been recalculated successfully!"
msgstr ""

msgid "Something went wrong. Please try again later."
msgstr ""

msgid "Include posts/pages where the score is already set"
msgstr ""

msgid "Calculating SEO score for posts %1$d - %2$d out of %3$d"
msgstr ""

msgid "Titles & Metas"
msgstr ""

msgid "Role Manager Settings"
msgstr ""

msgid "Settings File"
msgstr ""

msgid "Choose File"
msgstr ""

msgid "Please select a file to import."
msgstr ""

msgid "Import settings by locating settings file and clicking \"Import settings\"."
msgstr ""

msgid "Are you sure you want to import settings into Rank Math? Don't worry, your current configuration will be saved as a backup."
msgstr ""

msgid "Export Settings"
msgstr ""

msgid "Choose the panels to export."
msgstr ""

msgid "Plugin Settings"
msgstr ""

msgid "Import or export your Rank Math settings. This option is useful for replicating Rank Math settings across multiple websites. "
msgstr ""

msgid "Learn more about the Import/Export options."
msgstr ""

msgid "Import finished."
msgstr ""

msgid "No posts found with SEO score."
msgstr ""

msgid "Calculate SEO Scores"
msgstr ""

msgid "Are you sure you want to import data from %s?"
msgstr ""

msgid "Select data to import."
msgstr ""

msgid "Are you sure you want erase all traces of %s?"
msgstr ""

msgid "Clean"
msgstr ""

msgid "Other Plugins"
msgstr ""

msgid "If you were using another plugin to add important SEO information to your website before switching to Rank Math SEO, you can import the settings and data here. "
msgstr ""

msgid "No plugin detected with importable data."
msgstr ""

msgid "Create Backup"
msgstr ""

msgid "Settings Backup"
msgstr ""

msgid "Take a backup of your plugin settings in case you wish to restore them in future. Use it as backup before making substantial changes to Rank Math settings. For taking a backup of the SEO data of your content, use the XML Export option."
msgstr ""

msgid "Backup: %s"
msgstr ""

msgid "Are you sure you want to restore this backup? Your current configuration will be overwritten."
msgstr ""

msgid "Are you sure you want to delete this backup?"
msgstr ""

msgid "There is no backup."
msgstr ""

msgid "System Status"
msgstr ""

msgid "Error Log"
msgstr ""

msgid "If you have %s enabled, errors will be stored in a log file. Here you can find the last 100 lines in reversed order so that you or the Rank Math support team can view it easily. The file cannot be edited here."
msgstr ""

msgid "Copy Log to Clipboard"
msgstr ""

msgid "System Info"
msgstr ""

msgid "Copy System Info to Clipboard"
msgstr ""

msgctxt "block title"
msgid "FAQ by Rank Math"
msgstr ""

msgctxt "block description"
msgid "Easily add Schema-ready, SEO-friendly, Frequently Asked Questions to your content."
msgstr ""

msgctxt "block keyword"
msgid "FAQ"
msgstr ""

msgctxt "block keyword"
msgid "Frequently Asked Questions"
msgstr ""

msgctxt "block keyword"
msgid "Schema"
msgstr ""

msgctxt "block keyword"
msgid "SEO"
msgstr ""

msgctxt "block keyword"
msgid "Structured Data"
msgstr ""

msgctxt "block keyword"
msgid "Yoast"
msgstr ""

msgctxt "block keyword"
msgid "Rank Math"
msgstr ""

msgctxt "block keyword"
msgid "Block"
msgstr ""

msgctxt "block keyword"
msgid "Markup"
msgstr ""

msgctxt "block keyword"
msgid "Rich Snippet"
msgstr ""

msgctxt "block title"
msgid "HowTo by Rank Math"
msgstr ""

msgctxt "block description"
msgid "Easily add Schema-ready, SEO-friendly, HowTo block to your content."
msgstr ""

msgctxt "block keyword"
msgid "HowTo"
msgstr ""

msgctxt "block title"
msgid "Schema by Rank Math"
msgstr ""

msgctxt "block description"
msgid "Add the Schema generated by Rank Math anywhere on your page using this Block."
msgstr ""

msgctxt "block title"
msgid "Table of Contents by Rank Math"
msgstr ""

msgctxt "block description"
msgid "Automatically generate the Table of Contents from the Headings added to this page."
msgstr ""
